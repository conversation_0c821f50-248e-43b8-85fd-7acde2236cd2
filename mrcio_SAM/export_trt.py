import pycuda.driver as cuda #cuda和tensorrt放在cupy之前，否则运行会出错
import tensorrt as trt
from loguru import logger as log
import subprocess

def get_gpu_memory_in_gb(workspace,gpu_id=0):
    try:
        cuda.init()
        device = cuda.Device(gpu_id)  # 选择第一个 GPU 设备，您可以根据需要更改
        total_memory = device.total_memory()
        log.info("Device {}: {}".format(gpu_id, device.name()))
        log.info("Compute Capability: {}.{}".format(*device.compute_capability()))
        log.info("Total Memory: {} GB".format(device.total_memory() // (1024 ** 3)))
        memory_in_gb = total_memory // (1024**3)
        return memory_in_gb
    except Exception as e:
        log.info("获取显存大小出错: " + str(e))
        return workspace
class EngineBuilder:
    """
    Parses an ONNX graph and builds a TensorRT engine from it.
    """

    def __init__(self, verbose=False, workspace=4):
        """
        :param verbose: If enabled, a higher verbosity level will be set on the TensorRT logger.
        :param workspace: Max memory workspace to allow, in Gb.
        """
        self.trt_logger = trt.Logger(trt.Logger.INFO)
        if verbose:
            self.trt_logger.min_severity = trt.Logger.Severity.VERBOSE
        #如何workspace不是偶数，则自动转换成小于自己的偶数
        self.workspace = get_gpu_memory_in_gb(workspace)
        if self.workspace % 2 != 0:
            self.workspace = max(1,self.workspace - (self.workspace % 2))
        self.builder = trt.Builder(self.trt_logger)
        self.config = self.builder.create_builder_config()
        log.info(f"starting export with TensorRT {trt.__version__}...workspace {self.workspace}")
        self.is_trt10 = int(trt.__version__.split(".")[0]) >= 10  # is TensorRT >= 10
        if self.is_trt10:
            self.config.set_memory_pool_limit(trt.MemoryPoolType.WORKSPACE, self.workspace << 30)
        else:  # TensorRT versions 7, 8
            self.config.max_workspace_size = self.workspace * 1 << 30
        self.network = None
        self.parser = None
    def get_engine(self, onnx_model_name, trt_model_name,precision='fp32'):
        network_flags = (1 << int(trt.NetworkDefinitionCreationFlag.EXPLICIT_BATCH))
        try:
            self.network = self.builder.create_network(network_flags)
            self.parser = trt.OnnxParser(self.network, self.trt_logger)
            log.info('Loading ONNX file from path {}'.format(onnx_model_name))
            with open(onnx_model_name, 'rb') as model:
                log.info('Beginning ONNX file parsing')
                if not self.parser.parse(model.read()):
                    for error in range(self.parser.num_errors):
                        log.info(self.parser.get_error(error))
        except Exception as e:
            log.info(f"parser Error:{e}")
        log.info('Completed parsing of ONNX file')
        log.info('Building an engine from file {}; this may take a while...'.format(onnx_model_name))

        inputs = [self.network.get_input(i) for i in range(self.network.num_inputs)]
        outputs = [self.network.get_output(i) for i in range(self.network.num_outputs)]
        log.info("Network Description")
        log.info(f"num layers:{self.network.num_layers}")
        for input in inputs:
            log.info("Input '{}' with shape {} and dtype {}".format(input.name, input.shape, input.dtype))
            if input.name == "image_embeddings":
                log.info("convert SAM mobile_sam_simplify.onnx to engine")
                try:
                    command = ["./trtexec.exe",
                               f"--onnx={onnx_model_name}",
                               f"--saveEngine={trt_model_name}",
                               "--explicitBatch",
                               f"--workspace={self.workspace*1024}",
                               "--shapes=image_embeddings:1x256x64x64,point_coords:1x1x2,point_labels:1x1,mask_input:1x1x256x256,has_mask_input:1,orig_im_size:1x2",
                               "--minShapes=image_embeddings:1x256x64x64,point_coords:1x1x2,point_labels:1x1,mask_input:1x1x256x256,has_mask_input:1,orig_im_size:1x2",
                               "--optShapes=image_embeddings:1x256x64x64,point_coords:1x10x2,point_labels:1x10,mask_input:1x1x256x256,has_mask_input:1,orig_im_size:1x2",
                               "--maxShapes=image_embeddings:1x256x64x64,point_coords:1x20x2,point_labels:1x20,mask_input:1x1x256x256,has_mask_input:1,orig_im_size:1x2"]
                    log.info(f"maybe you can manual execution {' '.join(command)}")
                    creationflags = subprocess.CREATE_NO_WINDOW
                    proc = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.STDOUT,
                                            creationflags=creationflags)
                except:
                    log.info(r"切换到C:\TensorRT-*******\bin\trtexec.exe编译")
                    command = [r"C:\TensorRT-*******\bin\trtexec.exe",
                               f"--onnx={onnx_model_name}",
                               f"--saveEngine={trt_model_name}",
                               "--explicitBatch",
                               f"--workspace={self.workspace*1024}",
                               "--shapes=image_embeddings:1x256x64x64,point_coords:1x1x2,point_labels:1x1,mask_input:1x1x256x256,has_mask_input:1,orig_im_size:1x2",
                               "--minShapes=image_embeddings:1x256x64x64,point_coords:1x1x2,point_labels:1x1,mask_input:1x1x256x256,has_mask_input:1,orig_im_size:1x2",
                               "--optShapes=image_embeddings:1x256x64x64,point_coords:1x10x2,point_labels:1x10,mask_input:1x1x256x256,has_mask_input:1,orig_im_size:1x2",
                               "--maxShapes=image_embeddings:1x256x64x64,point_coords:1x20x2,point_labels:1x20,mask_input:1x1x256x256,has_mask_input:1,orig_im_size:1x2"]
                    log.info(f"maybe you can manual execution {' '.join(command)}")
                    creationflags = subprocess.CREATE_NO_WINDOW
                    proc = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.STDOUT,
                                            creationflags=creationflags)
                for line in proc.stdout:
                    line = line.decode().strip()
                    log.info(line)
                log.info("Completed creating Engine")
                return None

        for output in outputs:
            log.info("Output '{}' with shape {} and dtype {}".format(output.name, output.shape, output.dtype))
        manual_command = ["./trtexec.exe",
                   f"--onnx={onnx_model_name}",
                   f"--saveEngine={trt_model_name}",
                   "--explicitBatch",
                   f"--workspace={self.workspace * 1024}"]
        try:
            profile = self.builder.create_optimization_profile()
            for input in inputs:
                if -1 in input.shape and input.name in ["images","x"]:
                    if input.name == "images":
                        model_shape = {"min": 320, "opt": 960, "max": 2560}
                        profile.set_shape(input.name, (1, 3, model_shape["min"], model_shape["min"]),
                                          (1, 3, model_shape["opt"], model_shape["opt"]),
                                          (1, 3, model_shape["max"], model_shape["max"]))
                        manual_command.append(f"--optShapes={input.name}:1x3x{model_shape['opt']}x{model_shape['opt']}")
                        manual_command.append(f"--minShapes={input.name}:1x3x{model_shape['min']}x{model_shape['min']}")
                        manual_command.append(f"--maxShapes={input.name}:1x3x{model_shape['max']}x{model_shape['max']}")
                    elif input.name == "x":
                        model_shape = {"min": 1, "opt": 512, "max": 4096}
                        profile.set_shape(input.name, (1, 3, model_shape["min"], model_shape["min"]),
                                          (1, 3, model_shape["opt"], model_shape["opt"]),
                                          (1, 3, model_shape["max"], model_shape["max"]))
                        manual_command.append(f"--optShapes={input.name}:1x3x{model_shape['opt']}x{model_shape['opt']}")
                        manual_command.append(f"--minShapes={input.name}:1x3x{model_shape['min']}x{model_shape['min']}")
                        manual_command.append(f"--maxShapes={input.name}:1x3x{model_shape['max']}x{model_shape['max']}")
                    else:
                        log.error(f"cann't find input name {input.name} {input.shape}")
            self.config.add_optimization_profile(profile)
            if precision == "fp16":
                if not self.builder.platform_has_fast_fp16:
                    log.info("FP16 is not supported natively on this platform/device")
                else:
                    self.config.set_flag(trt.BuilderFlag.FP16)
            build = self.builder.build_serialized_network if self.is_trt10 else self.builder.build_engine
            with build(self.network, self.config) as engine, open(trt_model_name, "wb+") as t:
                t.write(engine if self.is_trt10 else engine.serialize())
            log.info(f"engine:{trt_model_name}")
            log.info("Completed creating Engine")
        except Exception as e:
            log.info(f"auto builder Error:{e}")
            try:
                creationflags = subprocess.CREATE_NO_WINDOW
                proc = subprocess.Popen(manual_command, stdout=subprocess.PIPE, stderr=subprocess.STDOUT,
                                        creationflags=creationflags)
                for line in proc.stdout:
                    line = line.decode().strip()
                    log.info(line)
                log.info("script Completed creating Engine")
            except Exception as e:
                log.info(f"script builder Error:{e}")
                log.info(f"maybe you can manual execution {' '.join(manual_command)}")

if __name__ == '__main__':
    builder = EngineBuilder(verbose=False)
    #builder.get_engine("best.onnx", "best.trt")
    builder.get_engine("vit_t_lm/optimized_sam-vit_t-image-decoder.onnx", "vit_t_lm/sam-vit_t-image-decoder.engine")
    #builder.get_engine("micro-sam-encoder.onnx", "micro-sam-encoder.engine")