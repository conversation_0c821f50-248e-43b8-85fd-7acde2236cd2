"""Copyright(c) 2023 lyuwenyu. All Rights Reserved."""
import cv2
import os
os.environ['PATH'] = os.environ['PATH'] + ';C:\TensorRT-10.7.0.23\lib'
import pycuda.driver as cuda
import tensorrt as trt
import collections
from collections import OrderedDict
import numpy as np
from PIL import Image, ImageDraw
from sahi.slicing import slice_image
from sahi.prediction import ObjectPrediction
from lsnms import nms
class TRTInference(object):
    def __init__(self, engine_path, max_batch_size=32, verbose=False):
        print("TensorRT version: %s" % trt.__version__)
        # 初始化CUDA上下文
        cuda.init()
        self.ctx = cuda.Device(0).retain_primary_context()
        self.engine_path = engine_path
        self.max_batch_size = max_batch_size
        self.logger = trt.Logger(trt.Logger.VERBOSE) if verbose else trt.Logger(trt.Logger.INFO)
        # 加载引擎
        self.engine = self.load_engine(engine_path)
        self.context = self.engine.create_execution_context()
        # 绑定内存
        self.bindings,self.stream = self.get_bindings()
        self.bindings_addr = OrderedDict((n, v.ptr) for n, v in self.bindings.items())
        self.input_names = self.get_input_names()
        self.output_names = self.get_output_names()

    def load_engine(self, path):
        trt.init_libnvinfer_plugins(self.logger, '')
        with open(path, 'rb') as f, trt.Runtime(self.logger) as runtime:
            return runtime.deserialize_cuda_engine(f.read())

    def get_input_names(self):
        return [name for name in self.engine if self.engine.get_tensor_mode(name) == trt.TensorIOMode.INPUT]

    def get_output_names(self):
        return [name for name in self.engine if self.engine.get_tensor_mode(name) == trt.TensorIOMode.OUTPUT]

    def get_bindings(self,input=None):
        Binding = collections.namedtuple('Binding', ('name', 'dtype', 'shape', 'data', 'ptr'))
        bindings = OrderedDict()
        self.ctx.push()
        stream = cuda.Stream()  # 必须在上下文激活状态下创建
        for name in self.engine:
            shape = list(self.engine.get_tensor_shape(name))
            dtype = trt.nptype(self.engine.get_tensor_dtype(name))

            # 处理动态batch
            if -1 in shape:
                #print("处理动态batch")
                shape[shape.index(-1)] = input.shape[0] if input is not None else self.max_batch_size
                if self.engine.get_tensor_mode(name) == trt.TensorIOMode.INPUT:
                    self.context.set_input_shape(name, shape)

            # 修正：根据实际shape分配内存
            volume = trt.volume(shape) if -1 not in shape else self.max_batch_size * trt.volume(shape[1:])

            if self.engine.get_tensor_mode(name) == trt.TensorIOMode.INPUT:
                host_data = cuda.pagelocked_empty(shape, dtype)  # 保持多维形状
                device_ptr = cuda.mem_alloc(host_data.nbytes)
            else:
                host_data = cuda.pagelocked_empty(volume, dtype)
                device_ptr = cuda.mem_alloc(host_data.nbytes)

            bindings[name] = Binding(name, dtype, shape, host_data, device_ptr)
        self.ctx.pop()
        return bindings,stream

    def __call__(self, inputs_image):
        input_img, orig_size = preprocess_image(inputs_image)

        # 创建输入字典
        inputs = {
            'images': input_img,
            'orig_target_sizes': orig_size.reshape(1, 2)
        }

        # 处理输入数据
        for name in self.input_names:
            # 获取绑定信息
            binding = self.bindings[name]

            # 类型检查
            if inputs[name].dtype != binding.dtype:
                raise TypeError(f"Input {name} dtype mismatch. "
                                f"Expected {binding.dtype}, got {inputs[name].dtype}")

            # 动态shape处理
            if list(inputs[name].shape) != binding.shape:
                print(f"Adjusting shape for {name} from {binding.shape} to {inputs[name].shape}")
                #self.context.set_input_shape(name, inputs[name].shape)
                # 重新初始化绑定
                self.bindings,self.stream = self.get_bindings(input=inputs[name])
                self.bindings_addr = OrderedDict((n, v.ptr) for n, v in self.bindings.items())
                binding = self.bindings[name]  # 获取更新后的绑定

            # 数据拷贝
            np.copyto(binding.data, inputs[name])
            cuda.memcpy_htod_async(binding.ptr, binding.data, self.stream)

        # 执行推理
        self.context.execute_v2(
            bindings=[int(v.ptr) for v in self.bindings.values()]
        )

        # 处理输出数据
        outputs = {}
        for name in self.output_names:
            cuda.memcpy_dtoh_async(self.bindings[name].data, self.bindings[name].ptr, self.stream)
            outputs[name] = self.bindings[name].data.reshape(self.bindings[name].shape)

        self.stream.synchronize()
        return outputs
    def slice_detection(self, image_path, cut_size=640, conf_thres=0.6,iou_thres=0.25):
        # 读取原始图像
        image = Image.open(image_path).convert("RGB")
        # 存储所有检测结果
        all_predictions = []
        slice_result  = slice_image(
            image=image,
            slice_height=cut_size,
            slice_width=cut_size,
            overlap_height_ratio=0.2,
            overlap_width_ratio=0.2,
        )
        num_slices = len(slice_result)
        # 处理每个切片
        for slice_idx in range(num_slices):
            slice_img = slice_result.images[slice_idx]
            start_x, start_y = slice_result.starting_pixels[slice_idx]
            print(f"slice img {slice_img.shape} start_x:{start_x} start_y: {start_y}")
            # 执行推理
            outputs = self.__call__(slice_img)
            # 转换坐标为原图坐标系
            boxes = outputs['boxes'] # [N,4]
            scores = outputs['scores']  # [N]
            labels = outputs['labels']  # [N]

            # 过滤低置信度
            keep = scores > conf_thres
            boxes = boxes[keep]
            scores = scores[keep]
            labels = labels[keep]

            # 坐标偏移
            boxes[:, [0, 2]] += start_x
            boxes[:, [1, 3]] += start_y

            # 转换为ObjectPrediction
            for box, score, label in zip(boxes, scores, labels):
                all_predictions.append(ObjectPrediction(
                    bbox=box.tolist(),
                    score=float(score),
                    category_id=int(label),
                    category_name=str(label)  # 根据实际类别映射修改
                ))
        # if num_slices>1:
        #     full_outputs = self.__call__(image)
        #     full_boxes = full_outputs['boxes']
        #     full_scores = full_outputs['scores']
        #     full_labels = full_outputs['labels']
        #     keep = full_scores > conf_thres
        #     for box, score, label in zip(full_boxes[keep], full_scores[keep], full_labels[keep]):
        #         all_predictions.append(ObjectPrediction(
        #             bbox=box.tolist(),
        #             score=float(score),
        #             category_id=int(label),
        #             category_name=str(label)
        #         ))
        # NMS去重
        final_predictions = self.__apply_nms(all_predictions, iou_thres)
        # 可视化结果
        boxes = np.array([pred.bbox.to_xyxy() for pred in final_predictions])
        draw = ImageDraw.Draw(image)
        for box in boxes:
            center_x = (box[0] + box[2]) / 2
            center_y = (box[1] + box[3]) / 2
            max_axis = max(box[2] - box[0], box[3] - box[1])
            min_axis = min(box[2] - box[0], box[3] - box[1])
            draw.ellipse((center_x - max_axis / 2, center_y - min_axis / 2, center_x + max_axis / 2, center_y + min_axis / 2), outline='red')
        image.save(image_path[:-4]+f"_result.jpg")
        return final_predictions

    def __apply_nms(self, predictions, iou_threshold):
        """应用NMS合并结果"""
        boxes = np.array([pred.bbox.to_xyxy() for pred in predictions])
        scores = np.array([pred.score.value for pred in predictions])
        if len(boxes) == 0:
            return []
        keep_inds = nms(boxes, scores, iou_threshold=iou_threshold)

        return [predictions[i] for i in keep_inds]

def preprocess_image(image, target_size=(640, 640)):
    if isinstance(image, str):
        image = Image.open(image).convert('RGB')
    elif isinstance(image, Image.Image):
        image=image.copy()
    elif isinstance(image, np.ndarray):
        image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
    else:
        raise ValueError("Image path must be a string.")
    image_width, image_height = image.size
    orig_size = np.array([image_width, image_height], dtype=np.int64)
    # 调整大小并转换格式
    resized = image.resize(target_size)
    img_np = np.array(resized, dtype=np.float32) / 255.0  # 归一化
    img_np = img_np.transpose(2, 0, 1)[np.newaxis, ...]  # CHW格式并添加batch维度
    print(f"[Preprocess] input shape: {img_np.shape}, dtype: {img_np.dtype}")
    print(f"[Preprocess] Orig size: {orig_size}, dtype: {orig_size.dtype}")
    return img_np, orig_size

def draw_results(images, labels, boxes, scores, thrh=0.6):
    print(labels.shape,boxes.shape,scores.shape)
    for i, im in enumerate(images):
        draw = ImageDraw.Draw(im)
        valid = scores[i] > thrh

        for score, label, box in zip(scores[i][valid], labels[i][valid], boxes[i][valid]):
            draw.rectangle(box.tolist(), outline='red')
            #draw.text((box[0], box[1]), text=str(int(label)), fill='blue')

        im.save(f'result_{i}.jpg')

if __name__ == '__main__':
    # 初始化推理引擎
    detector = TRTInference("B_taipanlan0815_rtdetrv2_r18vd_120e_coco_best.engine")
    from imutils.paths import list_images
    for image_path in list_images("D:\detect\RT_DETR\预标-台盼蓝"):
        if "_result" in image_path:
            continue
        # SAHI切片检测
        predictions2 = detector.slice_detection(
            image_path=image_path,
            cut_size=320,
            conf_thres=0.1,
            iou_thres=0.1
        )