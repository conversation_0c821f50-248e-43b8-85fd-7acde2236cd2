import os
os.environ["OPENCV_IO_MAX_IMAGE_PIXELS"] = str(pow(2, 50))
import json
import sys
import time
from pathlib import Path
import cv2
import numpy as np
from sahi.slicing import slice_image
from utils.general import convert_from_cv2_to_image,convert_from_image_to_cv2
from utils.general import cell_struct, cell_list, channel_struct
from lsnms import nms as large_nms
from utils.GPU_Predictor_dynamic import GPUPredictorUtil
from utils.crop_main_body import crop_hole
import pycuda.driver as cuda #cuda和tensorrt放在cupy之前,否则运行会出错
import tensorrt as trt
from loguru import logger as log
import traceback
FILE = Path(__file__).absolute()
sys.path.append(FILE.parents[0].as_posix())
import math



def remove_saturation(img):
    log.info("转灰度图预测")
    gray_image = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    gray_3_channel = cv2.cvtColor(gray_image, cv2.COLOR_GRAY2BGR)
    return gray_3_channel



def computer_black(roi):
    gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
    height, width = roi.shape[:2]
    center_x = width // 2
    center_y = height // 2
    if gray[int(center_y), int(center_x)] < 5:
        return True
    gray_sum = gray.sum()
    if gray_sum < 10:
        return True
    else:
        return False


class SuspensionBFInferengine(GPUPredictorUtil):
    def __init__(self,
                 bf_model_file_path,
                 ):
        super().__init__()
        self.SumLabel = ["BF", "FL1", "FL2", "FL3", "FL4", "dead", "live"]
        self.shuxing = [1, 2, 3, 4, 5, 35, 34]
        if not os.path.exists(bf_model_file_path):
            log.error("model engine file is not exists in {}".format(bf_model_file_path))
        else:
            log.info("loading model from {}".format(bf_model_file_path))
        self.model_file_path = bf_model_file_path
        try:
            self._init_cuda_context()
            self._setup_logging()
            self.engine = self._load_engine(self.model_file_path)
            log.info(f"context shape {self.engine.create_execution_context().get_binding_shape(0)}")
        except Exception as e:
            log.info(f"加载TensorRT引擎失败:{str(e)}")
            from utils.export_trt import EngineBuilder
            if os.path.exists(self.model_file_path.replace(".engine", ".onnx")):
                log.info("find onnx model,try to load onnx model convert to engine")
                builder = EngineBuilder(verbose=False)
                builder.get_engine(self.model_file_path.replace(".engine", ".onnx"),self.model_file_path)
            else:
                log.error(
                    f"{self.model_file_path.replace('.engine', '.onnx')} file is not exists,not convert model to engine,pls check")
            self.engine = self._load_engine(self.model_file_path)
            self.context = self.engine.create_execution_context()
        self.model_is_dynamic = -1 in self.context.get_binding_shape(0)
        self.max_batch_size, self.max_shape = self._get_engine_profile()
        log.info(f"max_batch {self.max_batch_size} max_shape {self.max_shape} model_is_dynamic {self.model_is_dynamic}")
    def allocate_buffers(self, input_shape):
        self.inputs = []
        self.outputs = []
        self.allocations = []
        self.ctx.push()
        for i in range(self.engine.num_bindings):
            name = self.engine.get_binding_name(i)
            dtype = self.engine.get_binding_dtype(i)
            if self.engine.binding_is_input(i):
                if -1 in tuple(self.engine.get_binding_shape(i)):  # dynamic
                    self.context.set_binding_shape(i, tuple(input_shape))
            shape = tuple(self.context.get_binding_shape(i))
            size = np.dtype(trt.nptype(dtype)).itemsize
            for s in shape:
                size *= s
            allocation = cuda.mem_alloc(size)
            binding = {
                'index': i,
                'name': name,
                'dtype': np.dtype(trt.nptype(dtype)),
                'shape': list(shape),
                'allocation': allocation,
            }
            self.allocations.append(allocation)
            if self.engine.binding_is_input(i):
                self.inputs.append(binding)
            else:
                self.outputs.append(binding)
        self.ctx.pop()
        assert len(self.inputs) > 0
        assert len(self.outputs) > 0
        assert len(self.allocations) > 0
    def _load_engine(self, path):
        """加载TensorRT引擎"""
        with open(path, "rb") as f, trt.Runtime(trt.Logger(trt.Logger.ERROR)) as runtime:
            return runtime.deserialize_cuda_engine(f.read())
    def _get_engine_profile(self):
        """获取引擎优化配置"""
        input_names=[name for name in self.engine if self.engine.get_tensor_mode(name) == trt.TensorIOMode.INPUT]
        profile_idx = [i for i in range(self.engine.num_optimization_profiles)]
        shapes = self.engine.get_tensor_profile_shape(input_names[0], profile_idx[0])
        log.info(f"_get_engine_profile {shapes}")
        max_batch_size = shapes[2][0]  # 取max shape的batch维度
        max_shape = shapes[2][-1] # 取max shape的H/W维度
        return max_batch_size, max_shape
    def _init_cuda_context(self):
        """初始化CUDA上下文"""
        cuda.init()
        self.device = cuda.Device(0)
        self.ctx = self.device.retain_primary_context()

    def _setup_logging(self):
        """设置日志信息"""
        log.info(f"TensorRT Version: {trt.__version__}")
        log.info(f"Device: {self.device.name()}")
        log.info(f"Compute Capability: {self.device.compute_capability()}")
        log.info(f"Total Memory: {self.device.total_memory() // (1024 ** 3)} GB")
    def tensorrt_infer(self, input_image):
        try:
            self.allocate_buffers(input_image.shape)
            cuda.memcpy_htod(self.inputs[0]['allocation'], np.ascontiguousarray(input_image))
        except Exception as e:
            error_message = traceback.format_exc()
            log.error(f"engine error and reboot engine:{error_message}")
            self.release()
            self.__init__(self.model_file_path)
            self.allocate_buffers(input_image.shape)
            cuda.memcpy_htod(self.inputs[0]['allocation'], np.ascontiguousarray(input_image))
        self.context.execute_v2(self.allocations)
        output = np.zeros(self.outputs[0]['shape'], dtype=self.outputs[0]['dtype'])
        cuda.memcpy_dtoh(output, self.outputs[0]['allocation'])
        return output

    def letterbox(self, im, new_shape=(640, 640), color=(114, 114, 114), auto=True, scaleFill=False, scaleup=True,
                  stride=32):
        # Resize and pad image while meeting stride-multiple constraints
        shape = im.shape[:2]  # current shape [height, width]
        if isinstance(new_shape, int):
            new_shape = (new_shape, new_shape)
        # Scale ratio (new / old)
        r = min(new_shape[0] / shape[0], new_shape[1] / shape[1])
        if not scaleup:  # only scale down, do not scale up (for better val mAP)
            r = min(r, 1.0)

        # Compute padding
        ratio = r, r  # width, height ratios
        new_unpad = int(round(shape[1] * r)), int(round(shape[0] * r))
        dw, dh = new_shape[1] - new_unpad[0], new_shape[0] - new_unpad[1]  # wh padding
        if auto:  # minimum rectangle
            dw, dh = np.mod(dw, stride), np.mod(dh, stride)  # wh padding
        elif scaleFill:  # stretch
            dw, dh = 0.0, 0.0
            new_unpad = (new_shape[1], new_shape[0])
            ratio = new_shape[1] / shape[1], new_shape[0] / shape[0]  # width, height ratios

        dw /= 2  # divide padding into 2 sides
        dh /= 2
        if shape[::-1] != new_unpad:  # resize
            im = cv2.resize(im, new_unpad, interpolation=cv2.INTER_LINEAR)
        top, bottom = int(round(dh - 0.1)), int(round(dh + 0.1))
        left, right = int(round(dw - 0.1)), int(round(dw + 0.1))
        im = cv2.copyMakeBorder(im, top, bottom, left, right, cv2.BORDER_CONSTANT, value=color)  # add border
        return im, ratio, (dw, dh)

    def preprocess(self, img_src_bgr):
        """_summary_
        Args:
            img_src_bgr (numpy array uint8): bgr

        """
        orig_size =np.array(img_src_bgr.shape[:2],dtype=np.int64).reshape(1, 2)
        shape = max(640, max(img_src_bgr.shape))
        if shape % 32 != 0:
            shape = int(shape / 32) * 32
        if self.model_is_dynamic:
            shape = min(shape, self.max_shape)
        else:
            shape = self.max_shape
        letterbox_img = self.letterbox(img_src_bgr, shape, stride=64, auto=False)[0]  # padded resize
        img = letterbox_img.transpose(2, 0, 1)[::-1]  # hwc->chw,bgr->rgb
        input_data = np.ascontiguousarray(img)
        input_data = input_data.astype('float32')
        input_data /= 255
        if len(input_data.shape) == 3:
            input_data = input_data[None]  # expand for batch dim
        return input_data,orig_size

    def box_area(self, box):
        # box = xyxy(4,n)
        return (box[2] - box[0]) * (box[3] - box[1])

    def box_iou(self, box1, box2, eps=1e-7):
        (a1, a2), (b1, b2) = np.split(box1[:, None], 2,
                                      axis=2), np.split(box2, 2, axis=1)
        array = np.minimum(a2, b2) - np.maximum(a1, b1)
        inter = array.clip(0)
        inter = inter.prod(2)

        # IoU = inter / (area1 + area2 - inter)
        return inter / (self.box_area(box1.T)[:, None] +
                        self.box_area(box2.T) - inter + eps)

    def clip_boxes(self, boxes, shape):
        # Clip boxes (xyxy) to image shape (height, width)
        if isinstance(boxes, np.ndarray):  # faster individually
            # np.array (faster grouped)
            boxes[:, [0, 2]] = boxes[:, [0, 2]].clip(0, shape[1])  # x1, x2
            boxes[:, [1, 3]] = boxes[:, [1, 3]].clip(0, shape[0])  # y1, y2
        else:
            print("type wont be supported")

    def scale_boxes(self, img1_shape, boxes, img0_shape, ratio_pad=None):
        # Rescale boxes (xyxy) from img1_shape to img0_shape
        if ratio_pad is None:  # calculate from img0_shape
            gain = min(img1_shape[0] / img0_shape[0],
                       img1_shape[1] / img0_shape[1])  # gain  = old / new
            pad = (img1_shape[1] - img0_shape[1] * gain) / 2, (
                    img1_shape[0] - img0_shape[0] * gain) / 2  # wh padding
        else:
            gain = ratio_pad[0][0]
            pad = ratio_pad[1]

        boxes[:, [0, 2]] -= pad[0]  # x padding
        boxes[:, [1, 3]] -= pad[1]  # y padding
        boxes[:, :4] /= gain
        self.clip_boxes(boxes, img0_shape)
        return boxes

    def empty_like(self, x):
        """Creates empty torch.Tensor or np.ndarray with same shape as input and float32 dtype."""
        return np.empty_like(x, dtype=np.float32) # numpy only
    def xywh2xyxy(self, x):
        """
        Convert bounding box coordinates from (x, y, width, height) format to (x1, y1, x2, y2) format where (x1, y1) is the
        top-left corner and (x2, y2) is the bottom-right corner. Note: ops per 2 channels faster than per channel.

        Args:
            x (np.ndarray): The input bounding box coordinates in (x, y, width, height) format.

        Returns:
            y (np.ndarray): The bounding box coordinates in (x1, y1, x2, y2) format.
        """
        y = self.empty_like(x)  # faster than clone/copy
        xy = x[..., :2]  # centers
        wh = x[..., 2:] / 2  # half width-height
        y[..., :2] = xy - wh  # top left xy
        y[..., 2:] = xy + wh  # bottom right xy
        return y

    def non_max_suppression(self,
                            prediction,
                            conf_thres=0.25,
                            iou_thres=0.45,
                            max_det=1000,
                            nm=32):

        bs = prediction.shape[0]  # batch size
        nc = prediction.shape[2] - nm - 5  # number of classes
        xc = prediction[..., 4] > conf_thres  # candidates
        # print("bs",bs, "nc", nc, "xc", xc.shape)
        if nc > 1000:
            assert nc == 1000, f"nc={nc} 不是v5模型"
        # Settings
        # min_wh = 2  # (pixels) minimum box width and height
        max_wh = 7680  # (pixels) maximum box width and height
        max_nms = 30000  # maximum number of boxes into cv2.dnn.NMSBoxes
        time_limit = 0.5 + 0.05 * bs  # seconds to quit after
        redundant = True  # require redundant detections
        merge = False  # use merge-NMS,False

        t = time.time()
        mi = 5 + nc  # mask start index,117中,前面是85（80类cls score, 4box, 1个obj score）,后面是32(mask coeffient)

        # numpy array不支持空数组的调用
        # https://blog.csdn.net/weixin_31866177/article/details/107380707
        # https://bobbyhadz.com/blog/python-indexerror-index-0-is-out-of-bounds-for-axis-0-with-size-0
        # https://blog.csdn.net/weixin_38753213/article/details/106754787
        # 不能对数组的元素赋值,比如 a=np.empty((0,6)),a[0]=1,这样会出错
        # output = np.zeros((0, 6 + nm), np.float32) * bs
        # 因此我们使用列表-》append-》转为numpy array
        output = [np.zeros((0, 6 + nm))] * bs
        for xi, x in enumerate(prediction):  # image index, image inference

            # confidence, xc的shape:(1, 25200), xi = 0, x的shape:(25200, 117)
            # 下面这句话就是筛选出obj score > conf_thres的instances
            # 经过筛选后的x的shape为:(44, 117)
            x = x[xc[xi]]

            # If none remain process next image
            if not x.shape[0]:
                continue

            # Compute conf
            x[:, 5:] *= x[:, 4:5]  # conf = obj_conf * cls_conf

            # Box/Mask
            # center_x, center_y, width, height) to (x1, y1, x2, y2)
            box = self.xywh2xyxy(x[:, :4])  # shape[44, 4]
            # 计算宽高比并过滤
            w = box[:, 2] - box[:, 0]  # 宽度
            h = box[:, 3] - box[:, 1]  # 高度
            max_side = np.maximum(w, h)  # 最大边
            min_side = np.minimum(w, h)  # 最小边
            aspect_ratio = max_side / min_side
            valid_indices = (aspect_ratio <= 2.1)
            x = x[valid_indices]
            box = box[valid_indices]
            # zero columns if no masks,从第index=85(第86个数)开始至117
            mask = x[:, mi:]  # mask shape[44, 32]

            # Detections matrix nx6 (xyxy, conf, cls)

            # best class only
            # x[:, 5:mi]是去除了4box + 1obj score的,就是cls score的从5到85
            # 下面这个max的第一个参数1,表示axis=1,就是按照列进行筛选cls中的最大值,且返回索引.
            # keepdim 表示是否需要保持输出的维度与输入一样,keepdim=True表示输出和输入的维度一样,
            # keepdim=False表示输出的维度被压缩了,也就是输出会比输入低一个维度.
            # j:Get the class with the highest confidence
            conf, j = x[:, 5:mi].max(axis=1), x[:, 5:mi].argmax(axis=1)
            conf, j = conf.reshape(-1, 1), j.reshape(-1, 1)

            # x的shape从[44, 38]经过conf.reshape(-1) > conf_thres筛选后变为
            # [43, 38],且:38 = 4box + 1conf + 1类别id + 32coeffients
            x = np.concatenate((box, conf, j.astype(float), mask),
                               axis=1)[conf.reshape(-1) > conf_thres]

            # Check shape
            n = x.shape[0]  # number of boxes
            if not n:  # no boxes
                continue
            elif n > max_nms:  # excess boxes
                x = x[(-x[:, 4]).argsort()[:max_nms]]  # sort by confidence
            else:
                x = x[(-x[:, 4]).argsort()]  # sort by confidence

            # Batched NMS
            c = x[:, 5:6] * max_wh  # classes
            # boxes (offset by class), scores
            boxes, scores = x[:, :4] + c, x[:, 4]
            i = large_nms(boxes, scores, score_threshold=conf_thres, iou_threshold=iou_thres)
            # if i.shape[0] > max_det:  # limit detections
            #     i = i[:max_det]
            if merge and (1 < n < 3E3):  # Merge NMS (boxes merged using weighted mean)
                # update boxes as boxes(i,4) = weights(i,n) * boxes(n,4)
                iou = self.box_iou(boxes[i], boxes) > iou_thres  # iou matrix
                weights = iou * scores[None]  # box weights
                # equal tensor.float()
                # tt = np.dot(weights, x[:, :4]).astype(np.float32)
                x[i, :4] = np.dot(weights, x[:, :4]).astype(np.float32) / weights.sum(1, keepdims=True)  # merged boxes
                if redundant:
                    i = i[iou.sum(1) > 1]  # require redundancy

            output[xi] = x[i]
            #output.append(x[i])
            # if (time.time() - t) > time_limit:
            #     print(f'WARNING ⚠️ NMS time limit {time_limit:.3f}s exceeded')
            #     break  # time limit exceede
        return output

    def non_max_suppression_v8(self,
                               prediction,
                               conf_thres=0.25,
                               iou_thres=0.45,
                               max_det=1000,
                               nm=32):
        assert 0 <= conf_thres <= 1, f'Invalid Confidence threshold {conf_thres}, valid values are between 0.0 and 1.0'
        assert 0 <= iou_thres <= 1, f'Invalid IoU {iou_thres}, valid values are between 0.0 and 1.0'
        if isinstance(prediction,
                      (list, tuple)):  # YOLOv8 model in validation model, output = (inference_out, loss_out)
            prediction = prediction[0]  # select only inference output
        bs = prediction.shape[0]  # batch size
        nc = prediction.shape[1] - nm - 4  # number of classes
        mi = 4 + nc  # mask start index
        # xc = prediction[:, 4:mi].max(1) > conf_thres  # candidates
        xc = prediction[:, 4:mi].max(1) > conf_thres
        # Settings
        # min_wh = 2  # (pixels) minimum box width and height
        time_limit = 0.5 + 0.05 * bs  # seconds to quit after
        redundant = True  # require redundant detections
        merge = False  # use merge-NMS
        max_wh = 7680  # (pixels) maximum box width and height
        max_nms = 30000  # maximum number of boxes into torchvision.ops.nms()

        t = time.time()
        # output = [torch.zeros((0, 6), device=prediction.device)] * bs
        output = [np.zeros((0, 6 + nm))] * bs
        for xi, x in enumerate(prediction):  # image index, image inference
            # Apply constraints
            # x[((x[..., 2:4] < min_wh) | (x[..., 2:4] > max_wh)).any(1), 4] = 0  # width-height
            x = np.transpose(x)[xc[xi]]  # confidence

            # If none remain process next image
            if not x.shape[0]:
                continue

            # Detections matrix nx6 (xyxy, conf, cls)
            box, cls, mask = x[:, :4], x[:, 4:nc + 4], x[:, nc + 4:]

            # Box (center x, center y, width, height) to (x1, y1, x2, y2)
            box = self.xywh2xyxy(box)
            # 计算宽高比并过滤
            w = box[:, 2] - box[:, 0]  # 宽度
            h = box[:, 3] - box[:, 1]  # 高度
            max_side = np.maximum(w, h)  # 最大边
            min_side = np.minimum(w, h)  # 最小边
            aspect_ratio = max_side / min_side
            valid_indices = (aspect_ratio <= 2.1)
            x = x[valid_indices]
            box, cls, mask = x[:, :4], x[:, 4:nc + 4], x[:, nc + 4:]

            # Detections matrix nx6 (xyxy, conf, cls)
            conf = cls.max(1, keepdims=True)
            j_argmax = cls.argmax(1)
            j = j_argmax if j_argmax.shape == x[:, 5:].shape else \
                np.expand_dims(j_argmax, 1)  # for argmax(axis, keepdims=True)
            x = np.concatenate((box, conf, j.astype(np.float32), mask), 1)[conf.reshape(-1) > conf_thres]

            # Check shape
            n = x.shape[0]  # number of boxes
            if not n:  # no boxes
                continue
            # elif n > max_nms:  # excess boxes
            #     x_argsort = np.argsort(x[:, 4])[:max_nms] # sort by confidence
            #     x = x[x_argsort]
            x_argsort = np.argsort(x[:, 4])[::-1][:max_nms]  # sort by confidence
            x = x[x_argsort]
            # Batched NMS
            c = x[:, 5:6] * max_wh  # classes
            boxes, scores = x[:, :4] + c, x[:, 4]  # boxes (offset by class), scores

            #############################################
            # i = torchvision.ops.nms(boxes, scores, iou_thres)  # NMS
            i = large_nms(boxes, scores, iou_thres)
            ############################################

            # if i.shape[0] > max_det:  # limit detections
            #     i = i[:max_det]
            if merge and (1 < n < 3E3):  # Merge NMS (boxes merged using weighted mean)
                # update boxes as boxes(i,4) = weights(i,n) * boxes(n,4)
                iou = self.box_iou(boxes[i], boxes) > iou_thres  # iou matrix
                weights = iou * scores[None]  # box weights
                # equal tensor.float()
                # tt = np.dot(weights, x[:, :4]).astype(np.float32)
                x[i, :4] = np.dot(weights, x[:, :4]).astype(np.float32) / weights.sum(1, keepdims=True)  # merged boxes
                if redundant:
                    i = i[iou.sum(1) > 1]  # require redundancy
            output[xi] = x[i]
        return output

    def get_prediction(self, net_input_data,
                       img_orig_sizes,
                       shift_amount=None,
                       conf_thres: float = 0.5,  # confidence threshold
                       iou_thres: float = 0.45
                       ):
        if shift_amount is None:
            shift_amount = [np.array([0, 0])] * len(img_orig_sizes)
        pred_det = self.tensorrt_infer(net_input_data)
        output = []
        try:
            pred = self.non_max_suppression(pred_det,
                                            conf_thres,
                                            iou_thres, nm=0)
        except Exception as e:
            pred = self.non_max_suppression_v8(pred_det,
                                               conf_thres,
                                               iou_thres, nm=0)
            log.error(f"切换成v8解析:{e}")
        for i, det in enumerate(pred):
            if len(det):
                det[:, :4] = self.scale_boxes(net_input_data.shape[2:], det[:, :4], img_orig_sizes[i]).round()
                det[:, [0, 2]] += shift_amount[i][0]  # x方向偏移
                det[:, [1, 3]] += shift_amount[i][1]  # y方向偏移
                output.append(det)
        return output
    def diam_computer(self,prediction_result,conf_thres,iou_thres):
        avg_diam=0
        if len(prediction_result) > 0:
            det = np.concatenate(prediction_result, axis=0)
            log.info(f"整图识别-预测结果:{det.shape[0]}")
            try:
                keep = large_nms(det[:, :4], det[:, 4], iou_threshold=iou_thres, score_threshold=conf_thres)
            except Exception as e:
                log.error(f"large_nms {e},use naive_nms")
                from lsnms.nms import naive_nms
                keep = naive_nms(det[:, :4], det[:, 4], iou_threshold=iou_thres, score_threshold=conf_thres)
            det = det[keep]
            log.info(f"整图识别-nms后结果:{det.shape[0]}")
            if det.shape[0] < 10:
                return 0
            array_xyxy = det[:, :4]
            diameters = ((array_xyxy[:, 2] - array_xyxy[:, 0]) + (array_xyxy[:, 3] - array_xyxy[:, 1])) / 2
            if len(diameters) > 0: #避免气泡等极端值,拉高平均直径
                diameters = np.sort(diameters)
                Q1 = np.percentile(diameters, 25)
                Q3 = np.percentile(diameters, 75)
                IQR = Q3 - Q1
                upper_bound = Q3 + 1.5 * IQR
                filtered_diameters = diameters[diameters <= upper_bound]
                if len(filtered_diameters) > 0:
                    avg_diam = np.mean(filtered_diameters)
                else:
                    avg_diam = np.mean(diameters)
            else:
                avg_diam = 0
        log.info(f"整图识别-平均直径:{avg_diam}")
        return avg_diam

    def sice_detection(self, im0, cut_size, conf_thres, iou_thres,set_batch_size=4):
        durations_in_seconds = {}
        time_start = time.time()
        slice_image_result = slice_image(
            image=convert_from_cv2_to_image(im0.copy()),
            slice_height=cut_size,
            slice_width=cut_size,
            overlap_height_ratio=0.2,
            overlap_width_ratio=0.2,
        )
        num_slices = len(slice_image_result)
        durations_in_seconds["slice"] = time.time() - time_start
        # create prediction input
        num_batch = min(set_batch_size, self.max_batch_size)
        log.info(f"Number of slices:{num_slices} num_batch {num_batch}")
        # perform sliced prediction
        pred_time = time.time()
        object_prediction_list = []
        log.info("处理中...")
        num_group = math.ceil(num_slices / num_batch)
        for group_ind in range(num_group):
            start_idx = group_ind * num_batch
            end_idx = min((group_ind + 1) * num_batch, num_slices)  # 防止越界
            current_batch_size = end_idx - start_idx  # 实际当前批次数量
            # print(f"当前批次:{current_batch_size}")
            # 准备当前batch数据
            batch_images = []
            batch_orig_sizes = []
            shift_amount_list = []
            for slice_idx in range(start_idx, end_idx):
                slice_img = slice_image_result.images[slice_idx]
                start_coord = slice_image_result.starting_pixels[slice_idx]
                # 转换为OpenCV格式
                processed_img, ori_shape = self.preprocess(convert_from_image_to_cv2(slice_img))
                shift_amount_list.append(start_coord)
                # 预处理每个切片（保持单次调用）
                batch_images.append(processed_img)  # 每个元素是[1,C,H,W]
                batch_orig_sizes.append(ori_shape)
            # 堆叠图像数据 [B, C, H, W]
            batch_img_data = np.concatenate(batch_images, axis=0)
            batch_orig_sizes = np.concatenate(batch_orig_sizes, axis=0)
            # print(batch_img_data.shape, batch_orig_sizes.shape)
            prediction_result = self.get_prediction(
                net_input_data=batch_img_data,
                img_orig_sizes=batch_orig_sizes,
                shift_amount=shift_amount_list,
                conf_thres=conf_thres,
                iou_thres=iou_thres,
            )
            object_prediction_list.extend(prediction_result)
        if num_slices > 1:
            log.info("perform full prediction")
            processed_img, ori_shape = self.preprocess(im0)
            prediction_result = self.get_prediction(
                net_input_data=processed_img,
                img_orig_sizes=ori_shape,
                conf_thres=conf_thres,
                iou_thres=iou_thres,
            )
            cell_diam=self.diam_computer(prediction_result,conf_thres,iou_thres)
            if cell_diam > 40 and ("M1beads" in self.model_file_path) and max(im0.shape)<5000:
                log.info(f"M1beads diame {cell_diam} > 40,only perform full prediction")
                object_prediction_list=[]
            object_prediction_list.extend(prediction_result)
        object_prediction_list = [object_prediction for object_prediction in object_prediction_list if
                                  object_prediction is not None]
        cell_info = np.empty([0, 13])
        if len(object_prediction_list) > 0:
            det = np.concatenate(object_prediction_list, axis=0)
            durations_in_seconds["prediction"] = time.time() - pred_time
            nms_start = time.time()
            # 以下是新增的在合并成det之后分批执行NMS的代码部分
            nms_batch_num = 50000
            if det.shape[0] > nms_batch_num * 1.5:
                log.info(f"{det.shape[0]} > {nms_batch_num * 1.5},use naive_nms")
                from lsnms.nms import naive_nms
                keep = naive_nms(det[:, :4], det[:, 4], iou_threshold=iou_thres, score_threshold=conf_thres)
                det = det[keep]
            else:
                try:
                    log.info("开始执行large_nms")
                    keep = large_nms(det[:, :4], det[:, 4], iou_threshold=iou_thres, score_threshold=conf_thres)
                except Exception as e:
                    log.error(f"large_nms {e},use naive_nms")
                    from lsnms.nms import naive_nms
                    keep = naive_nms(det[:, :4], det[:, 4], iou_threshold=iou_thres, score_threshold=conf_thres)
                det = det[keep]
            log.info("nms处理完成")
            log.info(f"Number of Cell:{det.shape[0]}")
            durations_in_seconds["NMS"] = time.time() - nms_start
            array_xyxy = det[:, :4]
            cell_info = np.empty([len(det), 13])
            cell_info[:, 0] = ((array_xyxy[:, 0] + array_xyxy[:, 2]) / 2).astype(int)  # center x
            cell_info[:, 1] = ((array_xyxy[:, 1] + array_xyxy[:, 3]) / 2).astype(int)  # center y
            cell_info[:, 2] = ((array_xyxy[:, 2] - array_xyxy[:, 0]) + (array_xyxy[:, 3] - array_xyxy[:, 1])) / 4  # 半径
            cell_info[:, 3] = np.ones(len(det))  # 团属性
            cell_info[:, 4] = det[:, -1].astype(int)  # 标签
            cell_info[:, 5] = ((array_xyxy[:, 2] - array_xyxy[:, 0]) + (array_xyxy[:, 3] - array_xyxy[:, 1])) / 2  # 直径
            cell_info[:, 6] = array_xyxy[:, 0]  # tx
            cell_info[:, 7] = array_xyxy[:, 1]  # ty
            cell_info[:, 8] = array_xyxy[:, 2]  # bx
            cell_info[:, 9] = array_xyxy[:, 3]  # by
            cell_info[:, 10] = np.multiply((array_xyxy[:, 2] - array_xyxy[:, 0]),
                                           (array_xyxy[:, 3] - array_xyxy[:, 1]))  # 面积
            cell_info[:, 11] = np.maximum((array_xyxy[:, 2] - array_xyxy[:, 0]),
                                          (array_xyxy[:, 3] - array_xyxy[:, 1]))  # 长轴
            cell_info[:, 12] = np.minimum((array_xyxy[:, 2] - array_xyxy[:, 0]),
                                          (array_xyxy[:, 3] - array_xyxy[:, 1]))  # 短轴
        log.info(durations_in_seconds)
        return cell_info

    def detction(self,
                 input_im_path,
                 image_save_path,
                 data_save_path,
                 fcs_save_path,
                 cut_size: int = 640,
                 BF_conf_thrd=0.1,
                 BF_iou_thrd=0.1,
                 input_labelme_path=""):
        jyh_img = cv2.imdecode(np.fromfile(input_im_path, dtype=np.uint8), cv2.IMREAD_COLOR)
        try:
            if "_cut.jpg" in input_im_path:
                im0 = cv2.imdecode(np.fromfile(input_im_path[:-8] + "_merge_src.jpg", dtype=np.uint8), cv2.IMREAD_COLOR)
            else:
                im0 = cv2.imdecode(np.fromfile(input_im_path[:-4] + "_src.jpg", dtype=np.uint8), cv2.IMREAD_COLOR)
        except Exception as e:
            im0 = jyh_img
            log.info(f"不存在原图,使用均一化图 {e}")
        try:
            CutoutConfig=f"{os.sep.join(os.path.dirname(input_im_path).split(os.sep)[:-1])}\CutoutConfig.txt"
            if os.path.exists(
                    CutoutConfig.replace('D:', 'E:').replace('d:', 'e:').replace('imageTemp', 'imagesource')):
                CutoutConfig = CutoutConfig.replace('D:', 'E:').replace('d:', 'e:').replace('imageTemp',
                                                                                            'imagesource')
            log.info(f"检查CutoutConfig.txt {CutoutConfig.replace('imageTemp', 'imagesource')}")
            if os.path.exists(CutoutConfig.replace('imageTemp', 'imagesource')):
                with open(CutoutConfig.replace('imageTemp', 'imagesource'), "r") as f:
                    cut_info = f.read().split(",")
            else:
                log.error("不存在CutoutConfig.txt")
                cut_info = self.cut_info.split(",")
            log.info(f"cut info {cut_info}")
            crop_img = cv2.bitwise_and(im0, im0, mask=crop_hole(im0, kong_num=cut_info[0], lens=cut_info[1],
                                                                view_judge=int(cut_info[2])))
        except Exception as e:
            crop_img = im0
            log.error(f"crop hole fail {e}")
        all_cell_info = np.empty([0, 13])
        if os.path.exists(input_labelme_path):
            try:
                log.info(f"use {input_labelme_path} result computer info")
                with open(input_labelme_path, "r") as f:
                    labelme_json = json.load(f)
                shapes = labelme_json["shapes"]
                array_xyxy = np.empty([len(shapes), 6])
                for i in range(len(shapes)):
                    score = 1.0
                    if labelme_json["shapes"][i]["shape_type"] == "polygon":
                        points = labelme_json["shapes"][i]["points"]
                        xs = [p[0] for p in points]
                        ys = [p[1] for p in points]
                        minx = min(xs)
                        maxx = max(xs)
                        miny = min(ys)
                        maxy = max(ys)
                        # 补充下面的代码实现添加shape的外部矩形到cell_info
                        try:
                            class_id = int(labelme_json["shapes"][i]["label"])
                        except Exception as e:
                            class_id = 0
                        array_xyxy[i] = np.array([minx, miny, maxx, maxy, score, class_id])
                all_cell_info = np.empty([len(shapes), 13])
                all_cell_info[:, 0] = ((array_xyxy[:, 0] + array_xyxy[:, 2]) / 2).astype(int)  # center x
                all_cell_info[:, 1] = ((array_xyxy[:, 1] + array_xyxy[:, 3]) / 2).astype(int)  # center y
                all_cell_info[:, 2] = ((array_xyxy[:, 2] - array_xyxy[:, 0]) + (
                        array_xyxy[:, 3] - array_xyxy[:, 1])) / 4  # 半径
                all_cell_info[:, 3] = np.ones(len(shapes))  # 团属性
                all_cell_info[:, 4] = array_xyxy[:, -1]  # 标签
                all_cell_info[:, 5] = ((array_xyxy[:, 2] - array_xyxy[:, 0]) + (
                        array_xyxy[:, 3] - array_xyxy[:, 1])) / 2  # 直径
                all_cell_info[:, 6] = array_xyxy[:, 0]  # tx
                all_cell_info[:, 7] = array_xyxy[:, 1]  # ty
                all_cell_info[:, 8] = array_xyxy[:, 2]  # bx
                all_cell_info[:, 9] = array_xyxy[:, 3]  # by
                all_cell_info[:, 10] = np.multiply((array_xyxy[:, 2] - array_xyxy[:, 0]),
                                                   (array_xyxy[:, 3] - array_xyxy[:, 1]))  # 面积
                all_cell_info[:, 11] = np.maximum((array_xyxy[:, 2] - array_xyxy[:, 0]),
                                                  (array_xyxy[:, 3] - array_xyxy[:, 1]))  # 长轴
                all_cell_info[:, 12] = np.minimum((array_xyxy[:, 2] - array_xyxy[:, 0]),
                                                  (array_xyxy[:, 3] - array_xyxy[:, 1]))  # 短轴
            except Exception as e:
                error_msg = traceback.format_exc()
                log.error(f"labelme_json error {error_msg}")
        else:
            all_cell_info = self.sice_detection(crop_img, cut_size, BF_conf_thrd, BF_iou_thrd)
        try:
            self.send_response("MsgTime", self.msg_id, {"tid": self.task_id, "Time": int(all_cell_info.shape[0] * 0.01) + 300})
        except Exception as e:
            log.error(f"send MsgTime error:{e}")
        cell_infos = cell_list()
        for cell_num, (
                cellx, celly, radius, group_type, label_num, diam, xyxy_0, xyxy_1, xyxy_2, xyxy_3, Area, max_aix,
                min_aix) in enumerate(all_cell_info):
            if computer_black(crop_img[int(xyxy_1):int(xyxy_3), int(xyxy_0):int(xyxy_2)]):
                continue
            cell = cell_struct(
                channel_struct(group_type=group_type, type=self.shuxing[self.SumLabel.index("BF")]).get_det_info(
                    img=im0,
                    xyxy_0=int(
                        xyxy_0),
                    xyxy_1=int(
                        xyxy_1),
                    xyxy_2=int(
                        xyxy_2),
                    xyxy_3=int(
                        xyxy_3)),
                None, None)
            cell_infos.push_cell(cell)
        log.info(f"cell_infos info Number of Cell:{cell_infos.get_length()}")
        cell_infos.group_computer(0, distance_tred=1.1)
        im0 = cell_infos.draw_cell_info(jyh_img, 0)
        try:
            cv2.imencode('.jpg', im0)[1].tofile(image_save_path)
            log.info(f"save image success {image_save_path}")
        except Exception as e:
            log.error(f"save image error {e}")
        cell_infos.to_csv(data_save_path)
        cell_infos.to_csv(fcs_save_path)
        log.info("处理完成")
if __name__ == '__main__':
    model=SuspensionBFInferengine(bf_model_file_path=r"suspension_BF_APP\xuanfu_BF_single_label0725_agg_fintune_xuanfu_BF_single_label0722_agg_yolov5s\best.engine")
    from imutils.paths import list_images
    for im_path in list_images(r"H:\单明场计数\1"):
        if "result" in im_path:
            continue
        model.detction(input_im_path=im_path,image_save_path=im_path[:-4]+"_result.jpg",data_save_path=r"H:\单图图库\AI明场\A1_01_01_01_AI.csv",fcs_save_path=r"H:\单图图库\AI明场\A1_01_01_01_fcsAI.csv",input_labelme_path="")
