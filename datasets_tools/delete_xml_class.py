from imutils.paths import list_files
import xml.etree.ElementTree as ET

def xiugaixml(in_file,outfile,xiugai_label,set_label=True,replacelabel="A"):
    tree = ET.parse(in_file)
    root = tree.getroot()
    for object in root.findall('object'):  # 找到根节点下所有“object”节点
        name = str(object.find('name').text)  # 找到object节点下name子节点的值（字符串）
        # 如果name等于str，则删除该节点
        if (name in xiugai_label):
            if set_label:
                    object.find('name').text = replacelabel
            else:
                root.remove(object)
    # 检查是否存在labelmap中没有的类别
    for object in root.findall('object'):
        name = str(object.find('name').text)
        if name in xiugai_label:
            print(in_file + "---mod_file_fail-->" + name)
    tree.write(outfile)  # tree为文件，write写入新的文件中。
for in_file in [i for i in list(list_files(r"D:\C项目训练0707\Ctypb_huizhong_0718")) if i.endswith("xml")]:
    xiugaixml(in_file,in_file,["daed"],set_label=True,replacelabel="dead")
    print(in_file + "------------->done")

#Counter({'live': 16949, 'dead': 1437, 'daed': 48, 'ww': 3})
#Counter({'live': 16952, 'dead': 1437, 'daed': 48})
#Counter({'live': 16952, 'dead': 1485})