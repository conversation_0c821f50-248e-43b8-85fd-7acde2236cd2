import os
import subprocess

import yaml
import shutil
import argparse


def mkdir(path):
    if not os.path.exists(path):
        print("create path:", path)
        os.makedirs(path)


def read_txt_return_number(txt_path):
    with open(txt_path, "r") as f:
        lines = f.readlines()
    return len(lines)


def run_paddleseg(datasets, height, width, backbone, gpu, batch_size, iters, resume_model, save_interval, fenbu,
                  resize_flag):
    os.system("proxychains git pull")
    if backbone == "1":
        file = yaml.load(open('templates/pp_liteseg_stdc1_512x512.yml', 'r', encoding='utf-8'), Loader=yaml.FullLoader)
        save_file = 'custom_config/pp_liteseg_stdc%s_%s_%sx%s.yml' % (backbone, datasets, height, width)
        save_file_dir = 'pp_liteseg_stdc%s_%s_%sx%s.yml' % (backbone, datasets, height, width)
        for iter, minketp in enumerate(file["loss"]["types"]):
            file["loss"]["types"][iter]["min_kept"] = batch_size * height * width // 16
    elif backbone == "2":
        file = yaml.load(open('templates/pp_liteseg_stdc2_512x512.yml', 'r', encoding='utf-8'), Loader=yaml.FullLoader)
        save_file = 'custom_config/pp_liteseg_stdc%s_%s_%sx%s.yml' % (backbone, datasets, height, width)
        save_file_dir = 'pp_liteseg_stdc%s_%s_%sx%s.yml' % (backbone, datasets, height, width)
        for iter, minketp in enumerate(file["loss"]["types"]):
            file["loss"]["types"][iter]["min_kept"] = batch_size * height * width // 16
    elif backbone == "fcn":  # fcn
        file = yaml.load(open('templates/fcn_hrnetw18_bs4_SCL.yml', 'r', encoding='utf-8'), Loader=yaml.FullLoader)
        save_file = 'custom_config/fcn_hrnetw18_%s_%sx%s.yml' % (datasets, height, width)
        save_file_dir = 'fcn_hrnetw18_%s_%sx%s.yml' % (datasets, height, width)
    elif backbone == "ocrnet": # ocrnet
        file = yaml.load(open('templates/OCRnet_HRNet_W18.yml', 'r', encoding='utf-8'), Loader=yaml.FullLoader)
        save_file = 'custom_config/OCRnet_%s_%sx%s.yml' % (datasets, height, width)
        save_file_dir = 'OCRnet_%s_%sx%s.yml' % (datasets, height, width)
    elif backbone == "ms":  # MobileSeg
        file = yaml.load(open('templates/mobilesegnet_512x512.yml', 'r', encoding='utf-8'), Loader=yaml.FullLoader)
        save_file = 'custom_config/MobileSeg_%s_%sx%s.yml' % (datasets, height, width)
        save_file_dir = 'MobileSeg_%s_%sx%s.yml' % (datasets, height, width)
        for iter, minketp in enumerate(file["loss"]["types"]):
            file["loss"]["types"][iter]["min_kept"] = batch_size * height * width // 16
    elif backbone == "d3p":  # DeepLabV3P_ResNet18_vd
        file = yaml.load(open('templates/DeepLabV3P_ResNet18_vd.yml', 'r', encoding='utf-8'), Loader=yaml.FullLoader)
        save_file = 'custom_config/DeepLabV3P_%s_%sx%s.yml' % (datasets, height, width)
        save_file_dir = 'DeepLabV3P_%s_%sx%s.yml' % (datasets, height, width)
        file["model"]["num_classes"]=read_txt_return_number("data/%s/labels.txt" % datasets)
    elif backbone == "fastscnn":  # fastscnn
        file = yaml.load(open('templates/fastscnn.yml', 'r', encoding='utf-8'), Loader=yaml.FullLoader)
        save_file = 'custom_config/fastscnn_%s_%sx%s.yml' % (datasets, height, width)
        save_file_dir = 'fastscnn_%s_%sx%s.yml' % (datasets, height, width)
        file["model"]["num_classes"]=read_txt_return_number("data/%s/labels.txt" % datasets)
    elif backbone == "sfnet":  # sfnet_ResNet18_vd
        file = yaml.load(open('templates/sfnet_ResNet18_vd.yml', 'r', encoding='utf-8'), Loader=yaml.FullLoader)
        save_file = 'custom_config/sfnet_%s_%sx%s.yml' % (datasets, height, width)
        save_file_dir = 'sfnet_%s_%sx%s.yml' % (datasets, height, width)
    else:
        print("backbone type error")
    file["batch_size"] = batch_size
    file["iters"] = iters
    pre_list = file["train_dataset"]["transforms"]
    if resize_flag:
        file["train_dataset"]["transforms"] = [{'type': 'ResizeStepScaling', 'min_scale_factor': 0.5,
                                                'max_scale_factor': 1.25, 'scale_step_size': 0.25},
                                               {"type": "Resize", "target_size": [int(width), int(height)]}] + pre_list
    else:
        file["train_dataset"]["transforms"] = [{'type': 'ResizeStepScaling', 'min_scale_factor': 0.5,
                                                'max_scale_factor': 1.25, 'scale_step_size': 0.25},
                                               {"type": "RandomPaddingCrop",
                                                "crop_size": [int(width), int(height)]}] + pre_list
    file["train_dataset"]["dataset_root"] = "data/%s" % datasets
    file["train_dataset"]["train_path"] = "data/%s/train_list.txt" % datasets
    file["train_dataset"]["num_classes"] = read_txt_return_number("data/%s/labels.txt" % datasets)
    file["val_dataset"]["dataset_root"] = "data/%s" % datasets
    file["val_dataset"]["val_path"] = "data/%s/val_list.txt" % datasets
    file["val_dataset"]["num_classes"] = read_txt_return_number("data/%s/labels.txt" % datasets)
    if resume_model:
        print("start resume model from ", resume_model)
        resume_pretrain_model = os.path.join(resume_model, "model.pdparams")
        file["model"]["backbone"]["pretrained"] = resume_pretrain_model
        mkdir(os.path.join(save_file_dir[:-4], "resume_model"))
        if not os.path.exists(os.path.join(save_file_dir[:-4], "resume_model", resume_model)):
            shutil.copytree(resume_model, os.path.join(save_file_dir[:-4], "resume_model", resume_model))
    yaml.dump(file, open(save_file, 'w', encoding='utf-8'), default_flow_style=False, allow_unicode=True)
    if fenbu:
        runcmd="CUDA_VISIBLE_DEVICES=0,1 /data/anaconda3/envs/paddle/bin/python -m paddle.distributed.launch train.py --config %s.yml --num_workers 16 --do_eval --use_vdl --save_interval %s --save_dir %s"%(save_file[:-4],save_interval,save_file_dir[:-4])
    else:
        runcmd="CUDA_VISIBLE_DEVICES=%s /data/anaconda3/envs/paddle/bin/python train.py  --config %s.yml --num_workers 16 --do_eval --use_vdl --save_interval %s --save_dir %s" % (gpu, save_file[:-4], save_interval, save_file_dir[:-4])
    #os.system(runcmd)
    if os.path.exists(os.path.join(save_file_dir[:-4], "best_model/model.pdparams")):
        subprocess.run("CUDA_VISIBLE_DEVICES=1 /data/anaconda3/envs/paddle/bin/python onnx_openvino.py  --model_dir %s" % (save_file_dir[:-4]),shell=True)
        subprocess.run("CUDA_VISIBLE_DEVICES=1 /data/anaconda3/envs/paddle/bin/python onnx_openvino.py  --model_dir %s --height %s --width %s" % (save_file_dir[:-4], str(height), str(width)),shell=True)
    with open(os.path.join(save_file_dir[:-4], "convert_tensorrt.bat"), "w+") as f:
        f.write(
            r"C:\TensorRT-8.4.0.6\bin\trtexec.exe --onnx=best.onnx --saveEngine=best.engine --explicitBatch --workspace=8192" + "\n" + r"D:\TensorRT-8.4.0.6cuda11.7\bin\trtexec.exe --onnx=best.onnx --saveEngine=best.engine --explicitBatch --workspace=8192")
    if not os.path.exists(os.path.join(save_file_dir[:-4], "model.yml")):
        shutil.copy("templates/model.yml", os.path.join(save_file_dir[:-4], "model.yml"))
        deplyfile = yaml.load(open(os.path.join(save_file_dir[:-4], "model.yml"), 'r', encoding='utf-8'),
                              Loader=yaml.FullLoader)
        deplyfile["Transforms"]["Resize"]["target_size"] = [int(height), int(width)]
        yaml.dump(deplyfile, open(os.path.join(save_file_dir[:-4], "model.yml"), 'w', encoding='utf-8'),
                  default_flow_style=False, allow_unicode=True)


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--datasets', '--d', type=str, default='A', help='datasets')
    parser.add_argument('--height', '--h', type=int, default=512, help='height')
    parser.add_argument('--width', '--w', type=int, default=512, help='width')
    parser.add_argument('--backbone', '--bb', type=str, default='1', help='backbone')
    parser.add_argument('--gpu', type=str, default='0', help='gpu')
    parser.add_argument('--batch_size', '--bz', type=int, default=16, help='batch_size')
    parser.add_argument('--save_interval', type=int, default=500, help='save_interval')
    parser.add_argument('--iters', '--it', type=int, default=10000, help='iters')
    parser.add_argument('--resume_model', type=str, default='', help='resume_model')
    parser.add_argument('--resize', action='store_true', help='Resize traing data')
    parser.add_argument('--fenbu', "--f", action='store_true', help='fenbu train')
    args = parser.parse_args()
    run_paddleseg(args.datasets, args.height, args.width, args.backbone, args.gpu, args.batch_size, args.iters,
                  args.resume_model, args.save_interval, args.fenbu, args.resize)
# run_paddleseg("A",512,512,1,"0",16,20000)


# import os
# from six import text_type as _text_type
# import argparse
#
#
# def export_onnx(model_dir, height, width):
#     if not os.path.exists(os.path.join(model_dir, "deploy_model")):
#         os.makedirs(os.path.join(model_dir, "deploy_model"))
#     if not os.path.exists(os.path.join(model_dir, "dynamic_deploy_model")):
#         os.makedirs(os.path.join(model_dir, "dynamic_deploy_model"))
#     save_dir = os.path.join(model_dir, "deploy_model")
#     dynamic_save_dir = os.path.join(model_dir, "dynamic_deploy_model")
#     best_openvino_dir = os.path.join(model_dir, "best_openvino_model")
#     best_openvino_dynamic_dir = os.path.join(model_dir, "best_openvino_model_dynamic")
#     # covert model to inference model
#     if height or width:
#         print("height",height,"width",width,"1")
#         export_cmd="/data/anaconda3/envs/paddlex2/bin/python export.py \
#                --config custom_config/{model_dir}.yml \
#                --model_path {model_dir}/best_model/model.pdparams \
#                --input_shape 1 3 {height} {width} \
#                --save_dir {save_dir}".format(model_dir=model_dir, height=height, width=width, save_dir=save_dir)
#         print(export_cmd)
#         os.system(export_cmd)
#         # convert paddle inference model to onnx
#         onnx_cmd="paddle2onnx --model_dir {save_dir} \
#                 --model_filename model.pdmodel  \
#                 --params_filename model.pdiparams  \
#                 --save_file {model_dir}/best.onnx  \
#                 --opset_version 11  \
#                 --enable_onnx_checker True".format(save_dir=save_dir, model_dir=model_dir)
#         print(onnx_cmd)
#         os.system(onnx_cmd)
#     else:
#         print("height",height,"width",width,"2")
#         export_cmd="/data/anaconda3/envs/paddlex2/bin/python export.py \
#                        --config custom_config/{model_dir}.yml \
#                        --model_path {model_dir}/best_model/model.pdparams \
#                        --input_shape -1 3 -1 -1 \
#                        --save_dir {dynamic_save_dir}".format(model_dir=model_dir, height=height, width=width,
#                                                              dynamic_save_dir=dynamic_save_dir)
#         print(export_cmd)
#         os.system(export_cmd)
#         onnx_cmd="paddle2onnx --model_dir {save_dir} \
#                 --model_filename model.pdmodel  \
#                 --params_filename model.pdiparams  \
#                 --save_file {model_dir}/best_dynamic.onnx  \
#                 --opset_version 11  \
#                 --enable_onnx_checker True".format(save_dir=dynamic_save_dir, model_dir=model_dir)
#         print(onnx_cmd)
#         os.system(onnx_cmd)
#     onnx_save_file = os.path.join(model_dir, "best.onnx")
#     onnx_save_dynamic_file = os.path.join(model_dir, "best_dynamic.onnx")
#     # convert onnx to openvino ir
#     try:
#         import openvino.inference_engine as ie
#         print(f'\n starting export with openvino {ie.__version__}...')
#         # import mo.main as mo
#         # from mo.utils.cli_parser import get_onnx_cli_parser
#     except Exception as e:
#         print("convert failed! ", e)
#         print(
#             "if error is 'no module name mo',please init openvino environment first"
#         )
#     else:
#         # openvino
#         if height or width:
#             shape = '[1,3,{height},{width}]'.format(height=height, width=width)
#             cmd = f"mo --input_model {onnx_save_file} --output_dir {best_openvino_dir} --input_shape {shape}"
#             # paddle_lite
#             armcmd = f"/data/anaconda3/envs/paddlex2/bin/paddle_lite_opt --model_dir={save_dir} --model_file={save_dir}/model.pdmodel \
#                     --param_file={save_dir}/model.pdiparams \
#                     --optimize_out_type=naive_buffer \
#                     --optimize_out={model_dir}/{model_dir} \
#                     --valid_targets=arm"
#         else:
#             shape = '[-1,3,{height},{width}]'.format(height=-1, width=-1)
#             cmd = f"mo --input_model {onnx_save_dynamic_file} --output_dir {best_openvino_dynamic_dir} --input_shape {shape}"
#             # paddle_lite
#             armcmd = f"/data/anaconda3/envs/paddlex2/bin/paddle_lite_opt --model_dir={save_dir} --model_file={save_dir}/model.pdmodel \
#                     --param_file={save_dir}/model.pdiparams \
#                     --optimize_out_type=naive_buffer \
#                     --optimize_out={model_dir}/{model_dir}_dynamic \
#                     --valid_targets=arm"
#         print(cmd)
#         print(armcmd)
#         os.system(cmd)
#         os.system(armcmd)
#
#
# if __name__ == '__main__':
#     parser = argparse.ArgumentParser(description='export model')
#     parser.add_argument('--model_dir', type=_text_type, default=None, help='model dir')
#     parser.add_argument('--height', '--h', type=int, default=None, help='height')
#     parser.add_argument('--width', '--w', type=int, default=None, help='width')
#     args = parser.parse_args()
#     export_onnx(args.model_dir, args.height, args.width)
