import random
import shutil

import matplotlib.pyplot as plt
from PIL import Image, ImageDraw
from imutils.paths import list_images
import os

import cv2
import numpy as np


def get_color_map_list(num_classes):
    color_map = num_classes * [0, 0, 0]
    for i in range(0, num_classes):
        j = 0
        lab = i
        while lab:
            color_map[i * 3] |= (((lab >> 0) & 1) << (7 - j))
            color_map[i * 3 + 1] |= (((lab >> 1) & 1) << (7 - j))
            color_map[i * 3 + 2] |= (((lab >> 2) & 1) << (7 - j))
            j += 1
            lab >>= 3
    return color_map
for i,j in enumerate(list(list_images(r"D:\B项目补充数据0701\边框数据\biankuang_data_FL\JPEGImages"))):
    if "label" in j:
        continue
    # if j.endswith(".png"):
    #     print(i, j)
    #     im = cv2.imdecode(np.fromfile(j, dtype=np.uint8), cv2.IMREAD_UNCHANGED)
    #     cv2.imencode('.jpg', im)[1].tofile(j[:-4]+".jpg")
    #     os.remove(j)
    # # shutil.copy(j,r"D:\C框不中数据\huizong\{}".format(str(i)+j.split(os.sep)[-1]))
    #
    # random1=512
    # random2=832
    # rand1=random.randint(0, im.shape[0] - random1)
    # rand2= random.randint(0, im.shape[0] - random2)
    # crop_list=[(rand2,rand2+random2,0,random2),(rand1,rand1+random1,im.shape[1]-random1,im.shape[1]),(0,1193,0,1417)]
    # random.shuffle(crop_list)
    # crop_list=crop_list[0]
    # print(crop_list)
    # crop_img= im[crop_list[0]:crop_list[1], crop_list[2]:crop_list[3]]
    # # print(im.shape)
    # # im=cv2.resize(im,(im.shape[1]//4,im.shape[0]//4))
    # # print(im.shape)
    # cv2.imencode('.jpg',crop_img)[1].tofile(os.path.join(r"D:\B项目补充数据0701\边框数据",j.split(os.sep)[-1][:-4]+"_cropbian1%s.jpg"%str(i)))
    img = np.asarray(Image.open(j))
    print(j,img.shape)
    mask = np.zeros(img.shape[:2], dtype=np.uint8)
    lbl = np.array(mask, dtype=bool)
    color_map = get_color_map_list(256)
    if lbl.min() >= 0 and lbl.max() <= 255:
        lbl_pil = Image.fromarray(lbl.astype(np.uint8), mode='P')
        lbl_pil.putpalette(color_map)
        print(np.unique(lbl_pil))
        lbl_pil.save(os.path.join(r"D:\B项目补充数据0701\边框数据\biankuang_data_FL\Annotations",j.split(os.sep)[-1][:-4]+".png"))