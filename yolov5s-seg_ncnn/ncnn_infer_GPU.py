import gc
import os

import ncnn

os.environ["OPENCV_IO_MAX_IMAGE_PIXELS"] = str(pow(2, 50))
from PIL import ImageFile
from PIL import Image
ImageFile.LOAD_TRUNCATED_IMAGES = True
Image.MAX_IMAGE_PIXELS = None
import time
from scipy import ndimage
#from lsnms.nms import naive_nms
from lsnms import nms as naive_nms
from tqdm import tqdm
import os.path as osp
import numpy as np
import pycuda.driver as cuda
import tensorrt as trt
import cv2
import cupyx.scipy.ndimage._interpolation as cusi
import cupy as cp
import logging
log=logging.getLogger("rmq_main.virusplque")
class Colors:
    # Ultralytics color palette https://ultralytics.com/
    def __init__(self):
        # hex = matplotlib.colors.TABLEAU_COLORS.values()
        hexs = ('FF3838', 'FF9D97', 'FF701F', 'FFB21D', 'CFD231', '48F90A', '92CC17', '3DDB86', '1A9334', '00D4BB',
                '2C99A8', '00C2FF', '344593', '6473FF', '0018EC', '8438FF', '520085', 'CB38FF', 'FF95C8', 'FF37C7')
        self.palette = [self.hex2rgb(f'#{c}') for c in hexs]
        self.n = len(self.palette)

    def __call__(self, i, bgr=False):
        c = self.palette[int(i) % self.n]
        return (c[2], c[1], c[0]) if bgr else c

    @staticmethod
    def hex2rgb(h):  # rgb order (PIL)
        return tuple(int(h[1 + i:1 + i + 2], 16) for i in (0, 2, 4))

from GPU_common_util import GPUPredictorUtil
class YOLOV5_SEG(GPUPredictorUtil):
    def __init__(self, model_file_path):
        super().__init__()
        self.logger = trt.Logger(trt.Logger.ERROR)
        if not os.path.exists(model_file_path):
            log.error("model engine file is not exists in {}".format(model_file_path),exc_info=True)
        log.info("loading model from {}".format(model_file_path))
        self.model_file_path=model_file_path
        self.net=ncnn.Net()
        self.net.load_param("seg_v8_ncnn0301/best.param")
        self.net.load_model("seg_v8_ncnn0301/best.bin")
        self.model_ex=self.net.create_extractor()


    def ncnn_infer(self, input_image):
        print(input_image.shape)
        self.model_ex.input("images", ncnn.Mat(input_image))
        _, out0 = self.model_ex.extract("output0")
        _, out1 = self.model_ex.extract("output1")
        pred_det=np.array(out0)
        proto=np.array(out1)
        print(pred_det.shape,proto.shape)
        return [pred_det, proto]

    def process_mask(self, protos, masks_in, bboxes, shape, upsample=False):
        """
        上采样之前先进行crop裁剪，再上采样（插值法）
        proto_out(ie. protos): [mask_dim, mask_h, mask_w],[32,160,160]
        out_masks(ie. masks_in): [n, mask_dim], n is number of masks after nms,[7,32]
        bboxes: [n, 4], n is number of masks after nms
        shape:input_image_size, (h, w)
        return: h, w, n
        """

        c, mh, mw = protos.shape  # CHW
        ih, iw = shape
        # @就是matmul的另一种写法，torch和numpy都有matmul,
        # masks_in:(3,32)、protos：(32,160,160)
        # 想要的ttt的shape为：[32, 3, 160]
        # ttt = np.matmul(masks_in, protos) #错误
        masks = self.sigmoid((masks_in @ protos.astype(np.float32).reshape(c, -1))).reshape(-1, mh, mw)  # CHW

        downsampled_bboxes = bboxes.copy()
        downsampled_bboxes[:, 0] *= mw / iw
        downsampled_bboxes[:, 2] *= mw / iw
        downsampled_bboxes[:, 3] *= mh / ih
        downsampled_bboxes[:, 1] *= mh / ih

        masks = self.crop_mask(masks, downsampled_bboxes)  # CHW
        # tt = masks.transpose(2, 1, 0)  # CHW->HWC,便于opencv的resize操作（仅可用于hwc）
        segments = []
        if upsample:
            # masks = ndimage.zoom(masks[None], (1, 1, 4, 4),
            #                      order=1,
            #                      mode="nearest")[0]
            # 使用numba的resize函数来优化upsample操作
            try:
                masks = cusi.zoom(cp.array(masks[None]), (1, 1, 4, 4), order=1, mode="nearest")[0]
                masks = cp.asnumpy(masks)
                masks = masks.__gt__(0.5).astype(np.float32)
                segments = self.masks2segments(masks)
                del masks
                segments = [self.scale_segments(self.input_data.shape[2:], x, self.image0.shape).round() for x in segments]
            except Exception as e:
                log.error("masks too large Out of GPU memory",exc_info=True)
                for mask in tqdm(masks):
                    mask = cusi.zoom(cp.array(mask[None]), (1, 4, 4), order=1, mode="nearest")
                    mask = cp.asnumpy(mask).__gt__(0.5).astype(np.float32)
                    chips_segments = self.masks2segments(mask)
                    del mask
                    segments.extend(
                        [self.scale_segments(self.input_data.shape[2:], x, self.image0.shape).round() for x in
                         chips_segments])
                del masks

        return segments  # 大于0.5的

    def letterbox(self, im, new_shape=(640, 640), color=(114, 114, 114), auto=True, scaleFill=False, scaleup=True,
                  stride=32):
        # Resize and pad image while meeting stride-multiple constraints
        shape = im.shape[:2]  # current shape [height, width]
        if isinstance(new_shape, int):
            new_shape = (new_shape, new_shape)
        # Scale ratio (new / old)
        r = min(new_shape[0] / shape[0], new_shape[1] / shape[1])
        if not scaleup:  # only scale down, do not scale up (for better val mAP)
            r = min(r, 1.0)

        # Compute padding
        ratio = r, r  # width, height ratios
        new_unpad = int(round(shape[1] * r)), int(round(shape[0] * r))
        dw, dh = new_shape[1] - new_unpad[0], new_shape[0] - new_unpad[1]  # wh padding
        if auto:  # minimum rectangle
            dw, dh = np.mod(dw, stride), np.mod(dh, stride)  # wh padding
        elif scaleFill:  # stretch
            dw, dh = 0.0, 0.0
            new_unpad = (new_shape[1], new_shape[0])
            ratio = new_shape[1] / shape[1], new_shape[0] / shape[0]  # width, height ratios

        dw /= 2  # divide padding into 2 sides
        dh /= 2
        if shape[::-1] != new_unpad:  # resize
            im = cv2.resize(im, new_unpad, interpolation=cv2.INTER_LINEAR)
        top, bottom = int(round(dh - 0.1)), int(round(dh + 0.1))
        left, right = int(round(dw - 0.1)), int(round(dw + 0.1))
        im = cv2.copyMakeBorder(im, top, bottom, left, right, cv2.BORDER_CONSTANT, value=color)  # add border
        return im, ratio, (dw, dh)

    def box_area(self, box):
        # box = xyxy(4,n)
        return (box[2] - box[0]) * (box[3] - box[1])

    def box_iou(self, box1, box2, eps=1e-7):
        """
        Return intersection-over-union (Jaccard index) of boxes.
        Both sets of boxes are expected to be in (x1, y1, x2, y2) format.
        Arguments:
            box1 (ndarray[N, 4])
            box2 (ndarray[M, 4])
        Returns:
            iou (ndarray[N, M]): the NxM matrix containing the pairwise
                IoU values for every element in boxes1 and boxes2
        """

        # inter(N,M) = (rb(N,M,2) - lt(N,M,2)).clamp(0).prod(2)
        (a1, a2), (b1, b2) = np.split(box1[:, None], 2,
                                      axis=2), np.split(box2, 2, axis=1)
        array = np.minimum(a2, b2) - np.maximum(a1, b1)
        inter = array.clip(0)
        inter = inter.prod(2)

        # IoU = inter / (area1 + area2 - inter)
        return inter / (self.box_area(box1.T)[:, None] +
                        self.box_area(box2.T) - inter + eps)

    def xywh2xyxy(self, x):
        # Convert nx4 boxes from [x, y, w, h] to [x1, y1, x2, y2] where xy1=top-left, xy2=bottom-right
        y = np.copy(x)
        y[:, 0] = x[:, 0] - x[:, 2] / 2  # top left x
        y[:, 1] = x[:, 1] - x[:, 3] / 2  # top left y
        y[:, 2] = x[:, 0] + x[:, 2] / 2  # bottom right x
        y[:, 3] = x[:, 1] + x[:, 3] / 2  # bottom right y
        return y

    def sigmoid(self, x):
        s = 1 / (1 + np.exp(-x))
        return s

    def skimage2opencv(self, src):
        src *= 255
        src.astype(int)
        return src

    def non_max_suppression(self,
                            prediction,
                            conf_thres=0.25,
                            iou_thres=0.45,
                            max_det=1000,
                            nm=32):

        bs = prediction.shape[0]  # batch size
        nc = prediction.shape[2] - nm - 5  # number of classes
        xc = prediction[..., 4] > conf_thres  # candidates

        # Settings
        # min_wh = 2  # (pixels) minimum box width and height
        max_wh = 7680  # (pixels) maximum box width and height
        max_nms = 30000  # maximum number of boxes into cv2.dnn.NMSBoxes
        time_limit = 0.5 + 0.05 * bs  # seconds to quit after
        redundant = True  # require redundant detections
        merge = False  # use merge-NMS，False

        t = time.time()
        mi = 5 + nc  # mask start index,117中，前面是85（80类cls score, 4box， 1个obj score），后面是32(mask coeffient)

        # numpy array不支持空数组的调用
        # https://blog.csdn.net/weixin_31866177/article/details/107380707
        # https://bobbyhadz.com/blog/python-indexerror-index-0-is-out-of-bounds-for-axis-0-with-size-0
        # https://blog.csdn.net/weixin_38753213/article/details/106754787
        # 不能对数组的元素赋值，比如 a=np.empty((0,6)),a[0]=1,这样会出错
        # output = np.zeros((0, 6 + nm), np.float32) * bs
        # 因此我们使用列表-》append-》转为numpy array
        output = []
        output_final = []
        for xi, x in enumerate(prediction):  # image index, image inference

            # confidence, xc的shape：(1, 25200), xi = 0, x的shape:(25200, 117)
            # 下面这句话就是筛选出obj score > conf_thres的instances
            # 经过筛选后的x的shape为：(44， 117)
            x = x[xc[xi]]

            # If none remain process next image
            if not x.shape[0]:
                continue

            # Compute conf
            x[:, 5:] *= x[:, 4:5]  # conf = obj_conf * cls_conf

            # Box/Mask
            # center_x, center_y, width, height) to (x1, y1, x2, y2)
            box = self.xywh2xyxy(x[:, :4])  # shape[44, 4]
            # zero columns if no masks,从第index=85(第86个数)开始至117
            mask = x[:, mi:]  # mask shape[44, 32]

            # Detections matrix nx6 (xyxy, conf, cls)

            # best class only
            # x[:, 5:mi]是去除了4box + 1obj score的，就是cls score的从5到85
            # 下面这个max的第一个参数1，表示axis=1，就是按照列进行筛选cls中的最大值，且返回索引。
            # keepdim 表示是否需要保持输出的维度与输入一样，keepdim=True表示输出和输入的维度一样，
            # keepdim=False表示输出的维度被压缩了，也就是输出会比输入低一个维度。
            # j:Get the class with the highest confidence
            conf, j = x[:, 5:mi].max(axis=1), x[:, 5:mi].argmax(axis=1)
            conf, j = conf.reshape(-1, 1), j.reshape(-1, 1)

            # x的shape从[44, 38]经过conf.reshape(-1) > conf_thres筛选后变为
            # [43, 38],且：38 = 4box + 1conf + 1类别id + 32coeffients
            x = np.concatenate((box, conf, j.astype(float), mask),
                               axis=1)[conf.reshape(-1) > conf_thres]

            # Check shape
            n = x.shape[0]  # number of boxes
            if not n:  # no boxes
                continue
            elif n > max_nms:  # excess boxes
                x = x[(-x[:, 4]).argsort()[:max_nms]]  # sort by confidence
            else:
                x = x[(-x[:, 4]).argsort()]  # sort by confidence

            # Batched NMS
            c = x[:, 5:6] * max_wh  # classes
            # boxes (offset by class), scores
            boxes, scores = x[:, :4] + c, x[:, 4]
            # i = cv2.dnn.NMSBoxes(boxes.tolist(), scores.tolist(),
            #                      self.conf_thres, self.iou_thres)  # NMS
            i = naive_nms(boxes, scores,score_threshold=conf_thres,iou_threshold=iou_thres)
            if i.shape[0] > max_det:  # limit detections
                i = i[:max_det]
            if merge and (1 < n <
                          3E3):  # Merge NMS (boxes merged using weighted mean)
                # update boxes as boxes(i,4) = weights(i,n) * boxes(n,4)
                iou = self.box_iou(boxes[i], boxes) > iou_thres  # iou matrix
                weights = iou * scores[None]  # box weights
                # equal tensor.float()
                # tt = np.dot(weights, x[:, :4]).astype(np.float32)

                x[i, :4] = np.dot(weights, x[:, :4]).astype(
                    np.float32) / weights.sum(1, keepdims=True)  # merged boxes
                if redundant:
                    i = i[iou.sum(1) > 1]  # require redundancy

            # output[xi] = x[i]
            output.append(x[i])
            # if (time.time() - t) > time_limit:
            #     print(f'WARNING ⚠️ NMS time limit {time_limit:.3f}s exceeded')
            #     break  # time limit exceeded

        output = np.array(output).reshape(-1, 6 + nm)
        output_final.append(output)

        return output_final

    def clip_boxes(self, boxes, shape):
        # Clip boxes (xyxy) to image shape (height, width)
        if isinstance(boxes, np.ndarray):  # faster individually
            # np.array (faster grouped)
            boxes[:, [0, 2]] = boxes[:, [0, 2]].clip(0, shape[1])  # x1, x2
            boxes[:, [1, 3]] = boxes[:, [1, 3]].clip(0, shape[0])  # y1, y2
        else:
            print("type wont be supported")

    def scale_boxes(self, img1_shape, boxes, img0_shape, ratio_pad=None):
        # Rescale boxes (xyxy) from img1_shape to img0_shape
        if ratio_pad is None:  # calculate from img0_shape
            gain = min(img1_shape[0] / img0_shape[0],
                       img1_shape[1] / img0_shape[1])  # gain  = old / new
            pad = (img1_shape[1] - img0_shape[1] * gain) / 2, (
                    img1_shape[0] - img0_shape[0] * gain) / 2  # wh padding
        else:
            gain = ratio_pad[0][0]
            pad = ratio_pad[1]

        boxes[:, [0, 2]] -= pad[0]  # x padding
        boxes[:, [1, 3]] -= pad[1]  # y padding
        boxes[:, :4] /= gain
        self.clip_boxes(boxes, img0_shape)
        return boxes

    def crop_mask(self, masks, boxes):
        """
        "Crop" predicted masks by zeroing out everything not in the predicted bbox.
        Vectorized by Chong (thanks Chong).
        Args:
            - masks should be a size [h, w, n] ndarray of masks
            - boxes should be a size [n, 4] ndarray of bbox coords in relative point form
        """

        n, h, w = masks.shape
        x1, y1, x2, y2 = np.split(boxes[:, :, None], 4, axis=1)  # x1 shape(n,1,1)
        r = np.arange(w, dtype=x1.dtype)[None, None, :]  # rows shape(1,w,1)
        c = np.arange(h, dtype=x1.dtype)[None, :, None]  # cols shape(h,1,1)
        return masks * ((r >= x1) * (r < x2) * (c >= y1) * (c < y2))


    def preprocess(self, img_src_bgr):
        """_summary_
        Args:
            img_src_bgr (numpy array uint8): bgr

        """
        self.image0 = img_src_bgr
        #shape = max(640, max(self.image0.shape) - max(self.image0.shape) % 32)
        shape = max(640,max(self.image0.shape))
        if shape%32!=0:
            shape=int(shape/32)*32
        shape = min(shape, self.max_shape)
        letterbox_img = self.letterbox(self.image0, shape, stride=64, auto=False)[0]  # padded resize
        img = letterbox_img.transpose(2, 0, 1)[::-1]  # hwc->chw,bgr->rgb
        self.input_data = np.ascontiguousarray(img)
        self.input_data = self.input_data.astype('float32')
        self.input_data /= 255
        if len(self.input_data.shape) == 3:
            self.input_data = self.input_data[None]  # expand for batch dim
        return self.input_data

    def detect(self,input_src,
               output_img_path,
               data_save_path,
               fcs_save_path,
               conf_threshold=0.2,
               iou_threshold=0.1,
               max_shape=1280,
               ):
        if isinstance(input_src,str):
            self.jyh_img = cv2.imdecode(np.fromfile(input_src, dtype=np.uint8), -1)
            try:
                img0=cv2.imdecode(np.fromfile(input_src[:-4]+"_src.jpg", dtype=np.uint8), -1)
            except:
                img0=self.jyh_img
                log.info("不存在均一化图，使用原图")
        elif isinstance(input_src,np.ndarray):
            img0=input_src
        else:
            log.error("error img",exc_info=True)
        # if max(img0.shape[0], img0.shape[1]) > input_size:
        #     self.scale = max(img0.shape[0], img0.shape[1]) / input_size
        #     resized_img = cv2.resize(img0, None, fx=1 / self.scale, fy=1 / self.scale)
        #     # del img0
        #     # resized_img=cv2.resize(img0,(input_size,input_size))
        # else:
        #     self.scale = 1
        #     resized_img = img0.copy()
        self.max_shape=max_shape
        self.max_det=2000
        durations_in_seconds = dict()
        time_start = time.time()
        print("img0.shape",img0.shape)
        net_input_data = self.preprocess(img0)
        pred_det, proto = self.ncnn_infer(net_input_data)
        print("pred_det.shape",pred_det.shape,"proto.shape",proto.shape)
        detection_start = time.time()
        durations_in_seconds["detection"] = detection_start-time_start
        pred = self.non_max_suppression(pred_det,
                                        conf_threshold,
                                        iou_threshold,
                                        self.max_det)
        nmsstart = time.time()
        durations_in_seconds["NMS"] = nmsstart-detection_start
        det=[]
        segments=[]
        for i, det in enumerate(pred):
            if len(det):
                segments = self.process_mask(
                    proto[i],
                    det[:, 6:],
                    det[:, :4],
                    self.input_data.shape[2:],
                    upsample=True)  # CHW,[instances_num, 640, 640]
                # Rescale boxes from img_size to im0 size
                # 就是将当前在letterbox等处理后的图像上检测的box结果，映射返回原图大小上
                det[:, :4] = self.scale_boxes(self.input_data.shape[2:], det[:, :4], self.image0.shape).round()
                print(det.shape[0],len(segments))
        get_maskstart = time.time()
        durations_in_seconds["get_mask"] = get_maskstart-nmsstart
        self.get_info(img0, det, segments,output_img_path,data_save_path,fcs_save_path)
        get_maskstart = time.time()
        durations_in_seconds["get_info"] = get_maskstart-nmsstart
        durations_in_seconds["cost time"]=time.time()-time_start
        log.info(durations_in_seconds)
        return 0

    def get_info(self,img,det,segments,output_img_path,data_save_path,fcs_save_path):
        anysis_reuslt_title = ','.join(['细胞编号', '属性值', '通道', 'X坐标', 'Y坐标', '团属性', '面积',
                                        '周长', '长轴', '短轴', '圆度', '边缘锐利度', '边缘差异率', '平均灰度',
                                        '累计灰度',
                                        '最大灰度', '最小灰度', '灰度差', '背景差异', '正圆率', '亮度']) + "\n"
        anysis_reuslt=""
        color_mask = np.zeros(img.shape, dtype=np.uint8)
        if len(det):
            boxes, scores, class_ids = det[:, :4], det[:, 4], det[:, 5]
            #color_mask = np.zeros(img, dtype=np.uint8)
            # color=Colors()
            #custom_color = color(random.randint(0, 255))
            custom_color=(0,100,0)
            for cell_num, (bbox, score, class_id) in enumerate(zip(boxes, scores, class_ids)):
                x1, y1, x2, y2 = bbox.astype(int)
                ctype = 1
                local_channel = 0
                center_x = int((x1 + x2) / 2)
                center_y = int((y1 + y2) / 2)
                group_type = ""  # 团属性
                major_axis_length = max((x2 - x1), (y2 - y1))
                minor_axis_length = min((x2 - x1), (y2 - y1))
                try:
                    area = cv2.contourArea(segments[cell_num])
                    color_mask = cv2.drawContours(color_mask,np.array([segments[cell_num]]).astype(int), -1,custom_color, -1)
                except Exception as e:
                    log.error("draw contour error",exc_info=True)
                    area=minor_axis_length*minor_axis_length
                res = [ctype, local_channel, center_x, center_y, group_type, int(area), "",
                       int(major_axis_length), int(minor_axis_length), "", "", "", "", "", "", "", "", "", "", ""]
                anysis_reuslt += str(cell_num + 1) + "," + ",".join(map(str, res)) + "\n"
        color_mask=(color_mask*0.8).astype(np.uint8)
        img = cv2.add(self.jyh_img,color_mask)
        del color_mask
        end_anysis_reuslt = anysis_reuslt_title + anysis_reuslt
        try:
            if os.path.exists(data_save_path):
                os.remove(data_save_path)
            with open(data_save_path, 'a+', encoding='utf-8') as f:
                f.write(end_anysis_reuslt)
        except Exception as e:
            log.error(f"data_save_path {data_save_path} not exists", exc_info=True)
        try:
            if fcs_save_path != '':
                if os.path.exists(fcs_save_path):
                    os.remove(fcs_save_path)
                with open(fcs_save_path, 'a+', encoding='utf-8') as f:
                    f.write(end_anysis_reuslt)
        except Exception as e:
            log.error(f"fcs_save_path {fcs_save_path} not exists", exc_info=True)
        cv2.imencode('.jpg', img)[1].tofile(output_img_path)

    def apply_mask(self, image, mask, color, alpha=0.5):
        """Apply the given mask to the image.
        """
        for c in range(3):
            image[:, :, c] = np.where(mask == 1,
                                      image[:, :, c] *
                                      (1 - alpha) + alpha * color[c],
                                      image[:, :, c])
        return image

    def masks2segments(self, masks, strategy='largest'):
        segments = []
        for x in masks:
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
            # # mask = cv2.dilate(x.astype("uint8"), kernel,2)MORPH_OPEN MORPH_OPEN
            # # mask = cv2.morphologyEx(x.astype("uint8"), cv2.MORPH_CLOSE, kernel)
            x = cv2.morphologyEx(x.astype("uint8"), cv2.MORPH_OPEN, kernel)
            # c = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)[0]
            mask = np.squeeze(x)
            mask = mask.astype(np.uint8)
            mask = cv2.copyMakeBorder(mask, 1, 1, 1, 1, cv2.BORDER_CONSTANT, value=0)
            c = cv2.findContours(mask, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE, offset=(-1, -1))[0]
            c=[cv2.approxPolyDP(x, (1e-100) * cv2.arcLength(x, True), True) for x in c] #直线变曲线
            # c = cv2.findContours(x.astype("uint8"), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)[0]
            if c:
                if strategy == 'concat':  # concatenate all segments
                    c = np.concatenate([x.reshape(-1, 2) for x in c])
                elif strategy == 'largest':  # select largest segment
                    c = np.array(c[np.array([len(x) for x in c]).argmax()]).reshape(-1, 2)
            else:
                c = np.zeros((0, 2))  # no segments found
            segments.append(c.astype('float32'))
        return segments

    def clip_segments(self, boxes, shape):
        # Clip segments (xy1,xy2,...) to image shape (height, width)
        boxes[:, 0] = boxes[:, 0].clip(0, shape[1])  # x
        boxes[:, 1] = boxes[:, 1].clip(0, shape[0])  # y

    def scale_segments(self, img1_shape, segments, img0_shape, ratio_pad=None):
        # Rescale coords (xyxy) from img1_shape to img0_shape
        if ratio_pad is None:  # calculate from img0_shape
            gain = min(img1_shape[0] / img0_shape[0], img1_shape[1] / img0_shape[1])  # gain  = old / new
            pad = (img1_shape[1] - img0_shape[1] * gain) / 2, (img1_shape[0] - img0_shape[0] * gain) / 2  # wh padding
        else:
            gain = ratio_pad[0][0]
            pad = ratio_pad[1]
        segments[:, 0] -= pad[0]  # x padding
        segments[:, 1] -= pad[1]  # y padding
        segments /= gain
        self.clip_segments(segments, img0_shape)
        return segments



if __name__ == "__main__":
    v5_seg_detector = YOLOV5_SEG("maskrcnn_model/xibaokongban_huizong_resize1960_seg.engine")
    #v5_seg_detector.detect(r"H:\单图图库\rshA2_01_BF_merge_10.17.jpg", "result.jpg", "tedas.csv", "sadas.csv", 0.1, 0.1)
    v5_seg_detector.detect("2_merge.jpg", "2_merge.jpg", r"H:\test\B2_01_res_B_origin_AI.csv", r"H:\test\B2_01_res_B_origin_AI.csv", 0.1, 0.1)

    # for inpath in list_images(r"D:\B项目数据1103\细胞团标图\xibaotuan_huizong"):
    #     #inpath=r"D:\B项目数据1103\病毒空斑标图_20221102\xibaokongban_huizong\rsqB2_01_BF_merge_A_1101.jpg"
    #     print(inpath)
    #     start_time=time.time()
    #     # 读入图像
    #     image0 = cv2.imdecode(np.fromfile(inpath, dtype=np.uint8), -1)
    #     # 进行推理(包含了预处理)
    #     prediction_out = v5_seg_detector.detect(inpath)
    #     # 后处理解码
    #     final_mask, final_det = v5_seg_detector.postprocess(prediction_out)
    #     # plt.imshow(final_mask[0,:,:])
    #     # plt.show()
    #     # print(inpath,image0.shape)
    #     # image0=cv2.resize(image0,(960,960))
    #     # v5_seg_detector.draw_mask(final_mask,im_src=image0)
    #     #v5_seg_detector.draw_detections(image0)
    #     v5_seg_detector.get_info(f"./result/{inpath.split(os.sep)[-1][:-4]}_origin.jpg",f"./result/{inpath.split(os.sep)[-1][:-4]}_data.csv",f"./result/{inpath.split(os.sep)[-1][:-4]}_fcs.csv")
    #     #cv2.imwrite(f"./result/{inpath.split(os.sep)[-1]}",image0)
    #     print(f"cost time {time.time()-start_time} s")
    #     #break
#C:\TensorRT-8.5.1.7\bin\trtexec.exe --onnx=best.onnx --saveEngine=best.engine --explicitBatch --workspace=4096 --minShapes=images:1x3x640x640 --optShapes=images:1x3x960x960 --maxShapes=images:1x3x4096x4096
#C:\TensorRT-8.5.1.7\bin\trtexec.exe --onnx=xiaochang_huizong117_2048_seg.onnx --saveEngine=xiaochang_huizong117_2048_seg.engine --explicitBatch --workspace=4096 --minShapes=images:1x3x640x640 --optShapes=images:1x3x960x960 --maxShapes=images:1x3x4096x4096