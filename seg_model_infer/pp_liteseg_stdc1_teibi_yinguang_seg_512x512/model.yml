Model: UNet
Transforms:
- Resize:
    target_size:
    - 512
    - 512
- Normalize:
    max_val:
    - 255.0
    - 255.0
    - 255.0
    mean:
    - 0.5
    - 0.5
    - 0.5
    min_val:
    - 0
    - 0
    - 0
    std:
    - 0.5
    - 0.5
    - 0.5
TransformsMode: RGB
_Attributes:
  eval_metrics:
    miou: 0.9022396766254397
  fixed_input_shape:
  - 512
  - 512
  labels:
  - _background_
  - A
  model_type: segmenter
  num_classes: 2
_ModelInputsOutputs:
  test_inputs:
  - - image
    - image
  test_outputs:
  - - pred
    - unsqueeze2_0.tmp_0
  - - logit
    - concat_5.tmp_0
_init_params:
  class_weight: null
  ignore_index: 255
  input_channel: 3
  num_classes: 2
  upsample_mode: bilinear
  use_bce_loss: true
  use_dice_loss: true
completed_epochs: 0
status: Infer
version: 1.3.11
