import json
import math
import time
from pathlib import Path
import os
os.environ["OPENCV_IO_MAX_IMAGE_PIXELS"] = str(pow(2, 50))
from sahi.slicing import slice_image
import sys
from utils.general import save_resultimage
from utils.crop_main_body import crop_hole
import numpy as np
import pycuda.driver as cuda #cuda和tensorrt放在cupy之前,否则运行会出错
import tensorrt as trt
from lsnms import nms as large_nms
import cv2
from utils.general import convert_from_cv2_to_image,convert_from_image_to_cv2
from utils.general import cell_struct, cell_list, channel_struct
from utils.GPU_Predictor_dynamic import GPUPredictorUtil
from loguru import logger as log
import traceback
FILE = Path(__file__).absolute()
sys.path.append(FILE.parents[0].as_posix())

class AOPImergeInferengine(GPUPredictorUtil):
    def __init__(self,
                 bf_model_file_path,
                 fl_model_file_path):
        super().__init__()
        self._init_cuda_context()
        self._setup_logging()
        self.pre_model_type = None
        self.model_type=None
        self.cut_info = None
        self.task_id = None
        self.root_url = None
        self.ws = None
        self.msg_id=None
        self.SumLabel = ["BF", "FL1", "FL2", "FL3", "FL4", "dead", "live"]
        self.shuxing = [1, 2, 3, 4, 5, 35, 34]
        # Model file paths
        self.model_file = {
            "BF": bf_model_file_path,
            "FL": fl_model_file_path,
        }
        self.fix_engine_shapes={
            "BF": 640,
            "FL": 640,
        }
        self.max_batch_sizes={
            "BF": 1,
            "FL": 1,
        }
        self.model_dynamic_flags = {
            "BF": True,
            "FL": True,
        }
        self.total_memory = self.calculate_total_memory(self.model_file)
        # Dynamic model loading based on VRAM size
        if self.vram_size > self.total_memory:
            # Load all models simultaneously if VRAM > 4GB
            log.info(f"VRAM {self.vram_size}GB > model_need_total_memory {self.total_memory}GB, loading all models at initialization")
            self.engines = {}
            self.contexts = {}
            for model_type in self.model_file.keys():
                if self.model_file[model_type]:
                    engine, context = self.init_engine(model_type)
                    self.engines[model_type] = engine
                    self.contexts[model_type] = context
            self.engine = None
            self.context = None
        else:
            # Load models on-demand if VRAM <= 4GB
            log.info(
                f"VRAM {self.vram_size}GB <= model_need_total_memory {self.total_memory}GB, models will be loaded on-demand")
            self.engine = None
            self.context = None
            self.engines = None
            self.contexts = None

    def calculate_total_memory(self, model_files):
        total_memory = 0
        for model_path in model_files.values():
            if model_path:  # 确保路径非空
                total_memory += self.estimate_model_memory(model_path)
        return round(total_memory / 1024, 2)  # Convert to GB

    def estimate_model_memory(self, engine_path):
        # 模型权重大小（MB）
        weight_size = os.path.getsize(engine_path) / (1024 ** 2)
        # 执行上下文显存占用（假设为权重的 10 倍）
        context_size = weight_size * 10
        # 总显存占用（MB）
        total_size = weight_size + context_size
        return total_size

    def init_engine(self, model_type):
        model_file_path = self.model_file[model_type]
        try:
            log.info(f"Loading model engine file: {model_file_path}")
            with open(model_file_path, "rb") as f, trt.Runtime(trt.Logger(trt.Logger.ERROR)) as runtime:
                engine = runtime.deserialize_cuda_engine(f.read())
            assert engine, "Engine deserialization failed"
            context = engine.create_execution_context()
            assert context, "Context creation failed"
            log.info(f"Context shape: {context.get_binding_shape(0)}")
        except Exception as e:
            log.error(f"Load model error: {e}")
            # Attempt to convert ONNX to engine if available
            onnx_path = model_file_path.replace(".engine", ".onnx")
            if os.path.exists(onnx_path):
                from utils.export_trt import EngineBuilder
                log.info(f"Found ONNX model, converting to engine: {onnx_path}")
                builder = EngineBuilder(verbose=False)
                builder.get_engine(onnx_path, model_file_path)
                with open(model_file_path, "rb") as f, trt.Runtime(trt.Logger(trt.Logger.ERROR)) as runtime:
                    engine = runtime.deserialize_cuda_engine(f.read())
                context = engine.create_execution_context()
                assert context, "Context creation failed"
                log.info(f"Context shape: {context.get_binding_shape(0)}")
            else:
                log.error(f"ONNX file not found: {onnx_path}")
                return None, None
        self.model_dynamic_flags[model_type] = -1 in context.get_binding_shape(0)
        self.max_batch_sizes[model_type],self.fix_engine_shapes[model_type] = self._get_engine_profile(engine)
        log.info(f"Model type: {model_type}, dynamic: {self.model_dynamic_flags[model_type]}, max_batch {self.max_batch_sizes[model_type]},fix_shape: {self.fix_engine_shapes[model_type]}")
        return engine, context

    def get_engine_context(self, model_type):
        """Retrieve or load engine and context based on VRAM size and model type."""
        if self.vram_size > self.total_memory:
            # Use pre-loaded engine and context
            if model_type in self.engines:
                return self.engines[model_type], self.contexts[model_type]
            else:
                log.error(f"Model {model_type} not pre-loaded")
                return None, None
        else:
            # Load model on-demand
            if self.engine is not None and self.pre_process_type == model_type:
                return self.engine, self.context
            else:
                log.info(f"Loading {model_type} model on-demand")
                if self.engine is not None:
                    del self.engine
                    del self.context
                self.engine, self.context = self.init_engine(model_type)
                self.pre_process_type = model_type
                return self.engine, self.context

    def _get_engine_profile(self, engine):
        """获取引擎优化配置"""
        input_names = [name for name in engine if engine.get_tensor_mode(name) == trt.TensorIOMode.INPUT]
        profile_idx = [i for i in range(engine.num_optimization_profiles)]
        shapes = engine.get_tensor_profile_shape(input_names[0], profile_idx[0])
        log.info(f"get_engine_profile {shapes}")
        max_batch_size = shapes[2][0]  # 取max shape的batch维度
        max_shape = shapes[2][-1]  # 取max shape的H/W维度
        return max_batch_size, max_shape

    def _init_cuda_context(self):
        """初始化CUDA上下文"""
        cuda.init()
        self.device = cuda.Device(0)
        self.ctx = self.device.retain_primary_context()
        self.vram_size = round(self.device.total_memory() // (1024 ** 3))  # Convert to GB

    def _setup_logging(self):
        """设置日志信息"""
        log.info(f"TensorRT Version: {trt.__version__}")
        log.info(f"Device: {self.device.name()}")
        log.info(f"Compute Capability: {self.device.compute_capability()}")
        log.info(f"Total Memory: {self.device.total_memory() // (1024 ** 3)} GB")
    def allocate_buffers(self, input_shape=None):
        self.inputs = []
        self.outputs = []
        self.allocations = []
        self.ctx.push()
        for i in range(self.engine.num_bindings):
            name = self.engine.get_binding_name(i)
            dtype = self.engine.get_binding_dtype(i)
            if self.engine.binding_is_input(i):
                if -1 in tuple(self.engine.get_binding_shape(i)):  # dynamic
                    self.context.set_binding_shape(i, tuple(input_shape))
            shape = tuple(self.context.get_binding_shape(i))
            size = np.dtype(trt.nptype(dtype)).itemsize
            for s in shape:
                size *= s
            allocation = cuda.mem_alloc(size)
            binding = {
                'index': i,
                'name': name,
                'dtype': np.dtype(trt.nptype(dtype)),
                'shape': list(shape),
                'allocation': allocation,
            }
            self.allocations.append(allocation)
            if self.engine.binding_is_input(i):
                self.inputs.append(binding)
            else:
                self.outputs.append(binding)
        self.ctx.pop()
        assert len(self.inputs) > 0
        assert len(self.outputs) > 0
        assert len(self.allocations) > 0

    def tensorrt_infer(self, input_image):
        self.allocate_buffers(input_image.shape)
        cuda.memcpy_htod(self.inputs[0]['allocation'], np.ascontiguousarray(input_image))
        self.context.execute_v2(self.allocations)
        output = np.zeros(self.outputs[0]['shape'], dtype=self.outputs[0]['dtype'])
        cuda.memcpy_dtoh(output, self.outputs[0]['allocation'])
        return output
    def letterbox(self, im, new_shape=(640, 640), color=(114, 114, 114), auto=True, scaleFill=False, scaleup=True,
                  stride=32):
        # Resize and pad image while meeting stride-multiple constraints
        shape = im.shape[:2]  # current shape [height, width]
        if isinstance(new_shape, int):
            new_shape = (new_shape, new_shape)
        # Scale ratio (new / old)
        r = min(new_shape[0] / shape[0], new_shape[1] / shape[1])
        if not scaleup:  # only scale down, do not scale up (for better val mAP)
            r = min(r, 1.0)

        # Compute padding
        ratio = r, r  # width, height ratios
        new_unpad = int(round(shape[1] * r)), int(round(shape[0] * r))
        dw, dh = new_shape[1] - new_unpad[0], new_shape[0] - new_unpad[1]  # wh padding
        if auto:  # minimum rectangle
            dw, dh = np.mod(dw, stride), np.mod(dh, stride)  # wh padding
        elif scaleFill:  # stretch
            dw, dh = 0.0, 0.0
            new_unpad = (new_shape[1], new_shape[0])
            ratio = new_shape[1] / shape[1], new_shape[0] / shape[0]  # width, height ratios

        dw /= 2  # divide padding into 2 sides
        dh /= 2
        if shape[::-1] != new_unpad:  # resize
            im = cv2.resize(im, new_unpad, interpolation=cv2.INTER_LINEAR)
        top, bottom = int(round(dh - 0.1)), int(round(dh + 0.1))
        left, right = int(round(dw - 0.1)), int(round(dw + 0.1))
        im = cv2.copyMakeBorder(im, top, bottom, left, right, cv2.BORDER_CONSTANT, value=color)  # add border
        return im, ratio, (dw, dh)

    def empty_like(self, x):
        """Creates empty torch.Tensor or np.ndarray with same shape as input and float32 dtype."""
        return np.empty_like(x, dtype=np.float32) # numpy only
    def xywh2xyxy(self, x):
        """
        Convert bounding box coordinates from (x, y, width, height) format to (x1, y1, x2, y2) format where (x1, y1) is the
        top-left corner and (x2, y2) is the bottom-right corner. Note: ops per 2 channels faster than per channel.

        Args:
            x (np.ndarray): The input bounding box coordinates in (x, y, width, height) format.

        Returns:
            y (np.ndarray): The bounding box coordinates in (x1, y1, x2, y2) format.
        """
        y = self.empty_like(x)  # faster than clone/copy
        xy = x[..., :2]  # centers
        wh = x[..., 2:] / 2  # half width-height
        y[..., :2] = xy - wh  # top left xy
        y[..., 2:] = xy + wh  # bottom right xy
        return y

    def box_area(self, box):
        # box = xyxy(4,n)
        return (box[2] - box[0]) * (box[3] - box[1])

    def box_iou(self, box1, box2, eps=1e-7):
        (a1, a2), (b1, b2) = np.split(box1[:, None], 2,
                                      axis=2), np.split(box2, 2, axis=1)
        array = np.minimum(a2, b2) - np.maximum(a1, b1)
        inter = array.clip(0)
        inter = inter.prod(2)

        # IoU = inter / (area1 + area2 - inter)
        return inter / (self.box_area(box1.T)[:, None] +
                        self.box_area(box2.T) - inter + eps)
    def preprocess(self, img_src_bgr):
        """_summary_
        Args:
            img_src_bgr (numpy array uint8): bgr

        """
        orig_size = np.array(img_src_bgr.shape[:2], dtype=np.int64).reshape(1, 2)
        shape = max(640, max(img_src_bgr.shape))
        if shape % 32 != 0:
            shape = int(shape / 32) * 32
        if self.model_dynamic_flags[self.model_type]:
            shape = min(shape, self.fix_engine_shapes[self.model_type])
        else:
            shape = self.fix_engine_shapes[self.model_type]
        letterbox_img = self.letterbox(img_src_bgr, shape, stride=64, auto=False)[0]  # padded resize
        img = letterbox_img.transpose(2, 0, 1)[::-1]  # hwc->chw,bgr->rgb
        input_data = np.ascontiguousarray(img)
        input_data = input_data.astype('float32')
        input_data /= 255
        if len(input_data.shape) == 3:
            input_data = input_data[None]  # expand for batch dim
        return input_data, orig_size
    def RemoveSaturation(self,img):
        log.info("转灰度图预测")
        gray_image = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        gray_3_channel = cv2.cvtColor(gray_image, cv2.COLOR_GRAY2BGR)
        return gray_3_channel
    def non_max_suppression(self,
                            prediction,
                            conf_thres=0.25,
                            iou_thres=0.45,
                            max_det=1000,
                            nm=32):

        bs = prediction.shape[0]  # batch size
        nc = prediction.shape[2] - nm - 5  # number of classes
        xc = prediction[..., 4] > conf_thres  # candidates
        # print("bs",bs, "nc", nc, "xc", xc.shape)
        if nc > 1000:
            assert nc == 1000, f"nc={nc} 不是v5模型"
        # Settings
        # min_wh = 2  # (pixels) minimum box width and height
        max_wh = 7680  # (pixels) maximum box width and height
        max_nms = 30000  # maximum number of boxes into cv2.dnn.NMSBoxes
        time_limit = 0.5 + 0.05 * bs  # seconds to quit after
        redundant = True  # require redundant detections
        merge = False  # use merge-NMS,False

        t = time.time()
        mi = 5 + nc  # mask start index,117中,前面是85（80类cls score, 4box, 1个obj score）,后面是32(mask coeffient)

        # numpy array不支持空数组的调用
        # https://blog.csdn.net/weixin_31866177/article/details/107380707
        # https://bobbyhadz.com/blog/python-indexerror-index-0-is-out-of-bounds-for-axis-0-with-size-0
        # https://blog.csdn.net/weixin_38753213/article/details/106754787
        # 不能对数组的元素赋值,比如 a=np.empty((0,6)),a[0]=1,这样会出错
        # output = np.zeros((0, 6 + nm), np.float32) * bs
        # 因此我们使用列表-》append-》转为numpy array
        output = [np.zeros((0, 6 + nm))] * bs
        for xi, x in enumerate(prediction):  # image index, image inference

            # confidence, xc的shape:(1, 25200), xi = 0, x的shape:(25200, 117)
            # 下面这句话就是筛选出obj score > conf_thres的instances
            # 经过筛选后的x的shape为:(44, 117)
            x = x[xc[xi]]

            # If none remain process next image
            if not x.shape[0]:
                continue

            # Compute conf
            x[:, 5:] *= x[:, 4:5]  # conf = obj_conf * cls_conf

            # Box/Mask
            # center_x, center_y, width, height) to (x1, y1, x2, y2)
            box = self.xywh2xyxy(x[:, :4])  # shape[44, 4]
            # 计算宽高比并过滤
            w = box[:, 2] - box[:, 0]  # 宽度
            h = box[:, 3] - box[:, 1]  # 高度
            max_side = np.maximum(w, h)  # 最大边
            min_side = np.minimum(w, h)  # 最小边
            aspect_ratio = max_side / min_side
            valid_indices = (aspect_ratio <= 2.1)
            x = x[valid_indices]
            box = box[valid_indices]
            # zero columns if no masks,从第index=85(第86个数)开始至117
            mask = x[:, mi:]  # mask shape[44, 32]

            # Detections matrix nx6 (xyxy, conf, cls)

            # best class only
            # x[:, 5:mi]是去除了4box + 1obj score的,就是cls score的从5到85
            # 下面这个max的第一个参数1,表示axis=1,就是按照列进行筛选cls中的最大值,且返回索引.
            # keepdim 表示是否需要保持输出的维度与输入一样,keepdim=True表示输出和输入的维度一样,
            # keepdim=False表示输出的维度被压缩了,也就是输出会比输入低一个维度.
            # j:Get the class with the highest confidence
            conf, j = x[:, 5:mi].max(axis=1), x[:, 5:mi].argmax(axis=1)
            conf, j = conf.reshape(-1, 1), j.reshape(-1, 1)

            # x的shape从[44, 38]经过conf.reshape(-1) > conf_thres筛选后变为
            # [43, 38],且:38 = 4box + 1conf + 1类别id + 32coeffients
            x = np.concatenate((box, conf, j.astype(float), mask),
                               axis=1)[conf.reshape(-1) > conf_thres]

            # Check shape
            n = x.shape[0]  # number of boxes
            if not n:  # no boxes
                continue
            elif n > max_nms:  # excess boxes
                x = x[(-x[:, 4]).argsort()[:max_nms]]  # sort by confidence
            else:
                x = x[(-x[:, 4]).argsort()]  # sort by confidence

            # Batched NMS
            c = x[:, 5:6] * max_wh  # classes
            # boxes (offset by class), scores
            boxes, scores = x[:, :4] + c, x[:, 4]
            i = large_nms(boxes, scores, score_threshold=conf_thres, iou_threshold=iou_thres)
            # if i.shape[0] > max_det:  # limit detections
            #     i = i[:max_det]
            if merge and (1 < n < 3E3):  # Merge NMS (boxes merged using weighted mean)
                # update boxes as boxes(i,4) = weights(i,n) * boxes(n,4)
                iou = self.box_iou(boxes[i], boxes) > iou_thres  # iou matrix
                weights = iou * scores[None]  # box weights
                # equal tensor.float()
                # tt = np.dot(weights, x[:, :4]).astype(np.float32)
                x[i, :4] = np.dot(weights, x[:, :4]).astype(np.float32) / weights.sum(1, keepdims=True)  # merged boxes
                if redundant:
                    i = i[iou.sum(1) > 1]  # require redundancy

            output[xi] = x[i]
            #output.append(x[i])
            # if (time.time() - t) > time_limit:
            #     print(f'WARNING ⚠️ NMS time limit {time_limit:.3f}s exceeded')
            #     break  # time limit exceede
        return output

    def non_max_suppression_v8(self,
                               prediction,
                               conf_thres=0.25,
                               iou_thres=0.45,
                               max_det=1000,
                               nm=32):
        assert 0 <= conf_thres <= 1, f'Invalid Confidence threshold {conf_thres}, valid values are between 0.0 and 1.0'
        assert 0 <= iou_thres <= 1, f'Invalid IoU {iou_thres}, valid values are between 0.0 and 1.0'
        if isinstance(prediction,
                      (list, tuple)):  # YOLOv8 model in validation model, output = (inference_out, loss_out)
            prediction = prediction[0]  # select only inference output
        bs = prediction.shape[0]  # batch size
        nc = prediction.shape[1] - nm - 4  # number of classes
        mi = 4 + nc  # mask start index
        # xc = prediction[:, 4:mi].max(1) > conf_thres  # candidates
        xc = prediction[:, 4:mi].max(1) > conf_thres
        # Settings
        # min_wh = 2  # (pixels) minimum box width and height
        time_limit = 0.5 + 0.05 * bs  # seconds to quit after
        redundant = True  # require redundant detections
        merge = False  # use merge-NMS
        max_wh = 7680  # (pixels) maximum box width and height
        max_nms = 30000  # maximum number of boxes into torchvision.ops.nms()

        t = time.time()
        # output = [torch.zeros((0, 6), device=prediction.device)] * bs
        output = [np.zeros((0, 6 + nm))] * bs
        for xi, x in enumerate(prediction):  # image index, image inference
            # Apply constraints
            # x[((x[..., 2:4] < min_wh) | (x[..., 2:4] > max_wh)).any(1), 4] = 0  # width-height
            x = np.transpose(x)[xc[xi]]  # confidence

            # If none remain process next image
            if not x.shape[0]:
                continue

            # Detections matrix nx6 (xyxy, conf, cls)
            box, cls, mask = x[:, :4], x[:, 4:nc + 4], x[:, nc + 4:]

            # Box (center x, center y, width, height) to (x1, y1, x2, y2)
            box = self.xywh2xyxy(box)
            # 计算宽高比并过滤
            w = box[:, 2] - box[:, 0]  # 宽度
            h = box[:, 3] - box[:, 1]  # 高度
            max_side = np.maximum(w, h)  # 最大边
            min_side = np.minimum(w, h)  # 最小边
            aspect_ratio = max_side / min_side
            valid_indices = (aspect_ratio <= 2.1)
            x = x[valid_indices]
            box, cls, mask = x[:, :4], x[:, 4:nc + 4], x[:, nc + 4:]

            # Detections matrix nx6 (xyxy, conf, cls)
            conf = cls.max(1, keepdims=True)
            j_argmax = cls.argmax(1)
            j = j_argmax if j_argmax.shape == x[:, 5:].shape else \
                np.expand_dims(j_argmax, 1)  # for argmax(axis, keepdims=True)
            x = np.concatenate((box, conf, j.astype(np.float32), mask), 1)[conf.reshape(-1) > conf_thres]

            # Check shape
            n = x.shape[0]  # number of boxes
            if not n:  # no boxes
                continue
            # elif n > max_nms:  # excess boxes
            #     x_argsort = np.argsort(x[:, 4])[:max_nms] # sort by confidence
            #     x = x[x_argsort]
            x_argsort = np.argsort(x[:, 4])[::-1][:max_nms]  # sort by confidence
            x = x[x_argsort]
            # Batched NMS
            c = x[:, 5:6] * max_wh  # classes
            boxes, scores = x[:, :4] + c, x[:, 4]  # boxes (offset by class), scores

            #############################################
            # i = torchvision.ops.nms(boxes, scores, iou_thres)  # NMS
            i = large_nms(boxes, scores, iou_thres)
            ############################################

            # if i.shape[0] > max_det:  # limit detections
            #     i = i[:max_det]
            if merge and (1 < n < 3E3):  # Merge NMS (boxes merged using weighted mean)
                # update boxes as boxes(i,4) = weights(i,n) * boxes(n,4)
                iou = self.box_iou(boxes[i], boxes) > iou_thres  # iou matrix
                weights = iou * scores[None]  # box weights
                # equal tensor.float()
                # tt = np.dot(weights, x[:, :4]).astype(np.float32)
                x[i, :4] = np.dot(weights, x[:, :4]).astype(np.float32) / weights.sum(1, keepdims=True)  # merged boxes
                if redundant:
                    i = i[iou.sum(1) > 1]  # require redundancy
            output[xi] = x[i]
        return output
    def clip_boxes(self, boxes, shape):
        # Clip boxes (xyxy) to image shape (height, width)
        if isinstance(boxes, np.ndarray):  # faster individually
            # np.array (faster grouped)
            boxes[:, [0, 2]] = boxes[:, [0, 2]].clip(0, shape[1])  # x1, x2
            boxes[:, [1, 3]] = boxes[:, [1, 3]].clip(0, shape[0])  # y1, y2
        else:
            print("type wont be supported")
    def scale_boxes(self, img1_shape, boxes, img0_shape, ratio_pad=None):
        # Rescale boxes (xyxy) from img1_shape to img0_shape
        if ratio_pad is None:  # calculate from img0_shape
            gain = min(img1_shape[0] / img0_shape[0],
                       img1_shape[1] / img0_shape[1])  # gain  = old / new
            pad = (img1_shape[1] - img0_shape[1] * gain) / 2, (
                    img1_shape[0] - img0_shape[0] * gain) / 2  # wh padding
        else:
            gain = ratio_pad[0][0]
            pad = ratio_pad[1]

        boxes[:, [0, 2]] -= pad[0]  # x padding
        boxes[:, [1, 3]] -= pad[1]  # y padding
        boxes[:, :4] /= gain
        self.clip_boxes(boxes, img0_shape)
        return boxes
    def get_prediction(self,net_input_data,
                       img_orig_sizes,
                       detection_model_type,
                       shift_amount=None,
                       conf_thres: float = 0.5,  # confidence threshold
                       iou_thres: float = 0.45
                       ):
        if shift_amount is None:
            shift_amount = [np.array([0, 0])] * len(img_orig_sizes)
        self.model_type=detection_model_type
        engine, context = self.get_engine_context(self.model_type)
        if engine is None or context is None:
            log.error(f"Failed to get engine/context for {self.model_type}")
            return None
        self.engine, self.context = engine, context
        self.pre_model_type = self.model_type
        pred_det = self.tensorrt_infer(net_input_data)
        output = []
        try:
            pred = self.non_max_suppression(pred_det,
                                            conf_thres,
                                            iou_thres, nm=0)
        except Exception as e:
            pred = self.non_max_suppression_v8(pred_det,
                                               conf_thres,
                                               iou_thres, nm=0)
            log.error(f"切换成v8解析:{e}")
        for i, det in enumerate(pred):
            if len(det):
                det[:, :4] = self.scale_boxes(net_input_data.shape[2:], det[:, :4], img_orig_sizes[i]).round()
                det[:, [0, 2]] += shift_amount[i][0]  # x方向偏移
                det[:, [1, 3]] += shift_amount[i][1]  # y方向偏移
                output.append(det)
        return output
    def sice_detection(self,im0, cut_size, detection_model_type, conf_thres, iou_thres, set_batch_size=4):
        durations_in_seconds = {}
        time_start = time.time()
        slice_image_result = slice_image(
            image=convert_from_cv2_to_image(im0.copy()),
            slice_height=cut_size,
            slice_width=cut_size,
            overlap_height_ratio=0.2,
            overlap_width_ratio=0.2,
        )
        num_slices = len(slice_image_result)
        durations_in_seconds["slice"] = time.time() - time_start
        # create prediction input
        self.model_type=detection_model_type
        num_batch = min(set_batch_size, self.max_batch_sizes[detection_model_type])
        log.info(f"Number of slices:{num_slices} num_batch {num_batch}")
        # perform sliced prediction
        pred_time = time.time()
        object_prediction_list = []
        log.info("处理中...")
        num_group = math.ceil(num_slices / num_batch)
        for group_ind in range(num_group):
            start_idx = group_ind * num_batch
            end_idx = min((group_ind + 1) * num_batch, num_slices)  # 防止越界
            current_batch_size = end_idx - start_idx  # 实际当前批次数量
            # print(f"当前批次:{current_batch_size}")
            # 准备当前batch数据
            batch_images = []
            batch_orig_sizes = []
            shift_amount_list = []
            for slice_idx in range(start_idx, end_idx):
                slice_img = slice_image_result.images[slice_idx]
                start_coord = slice_image_result.starting_pixels[slice_idx]
                # 转换为OpenCV格式
                processed_img, ori_shape = self.preprocess(convert_from_image_to_cv2(slice_img))
                shift_amount_list.append(start_coord)
                # 预处理每个切片（保持单次调用）
                batch_images.append(processed_img)  # 每个元素是[1,C,H,W]
                batch_orig_sizes.append(ori_shape)
            # 堆叠图像数据 [B, C, H, W]
            batch_img_data = np.concatenate(batch_images, axis=0)
            batch_orig_sizes = np.concatenate(batch_orig_sizes, axis=0)
            # print(batch_img_data.shape, batch_orig_sizes.shape)
            prediction_result = self.get_prediction(
                net_input_data=batch_img_data,
                img_orig_sizes=batch_orig_sizes,
                detection_model_type=detection_model_type,
                shift_amount=shift_amount_list,
                conf_thres=conf_thres,
                iou_thres=iou_thres,
            )
            object_prediction_list.extend(prediction_result)
        if num_slices > 1:
            log.info("perform full prediction")
            processed_img, ori_shape = self.preprocess(im0)
            prediction_result = self.get_prediction(
                net_input_data=processed_img,
                img_orig_sizes=ori_shape,
                detection_model_type=detection_model_type,
                conf_thres=conf_thres,
                iou_thres=iou_thres,
            )
            object_prediction_list.extend(prediction_result)
        object_prediction_list = [object_prediction for object_prediction in object_prediction_list if
                                  object_prediction is not None]
        cell_info = np.empty([0, 13])
        if len(object_prediction_list) > 0:
            det = np.concatenate(object_prediction_list, axis=0)
            durations_in_seconds["prediction"] = time.time() - pred_time
            nms_start = time.time()
            log.info("开始执行nms")
            # 以下是新增的在合并成det之后分批执行NMS的代码部分
            nms_batch_num = 50000
            if det.shape[0] > nms_batch_num * 1.5:
                log.info(f"{det.shape[0]} > {nms_batch_num * 1.5},use naive_nms")
                from lsnms.nms import naive_nms
                keep = naive_nms(det[:, :4], det[:, 4], iou_threshold=iou_thres, score_threshold=conf_thres)
                det = det[keep]
            else:
                try:
                    log.info("开始执行large_nms")
                    keep = large_nms(det[:, :4], det[:, 4], iou_threshold=iou_thres, score_threshold=conf_thres)
                except Exception as e:
                    log.error(f"large_nms {e},use naive_nms")
                    from lsnms.nms import naive_nms
                    keep = naive_nms(det[:, :4], det[:, 4], iou_threshold=iou_thres, score_threshold=conf_thres)
                det = det[keep]
            log.info("nms处理完成")
            log.info(f"Number of Cell:{det.shape[0]}")
            durations_in_seconds["NMS"] = time.time() - nms_start
            array_xyxy = det[:, :4]
            cell_info = np.empty([len(det), 13])
            cell_info[:, 0] = ((array_xyxy[:, 0] + array_xyxy[:, 2]) / 2).astype(int)  # center x
            cell_info[:, 1] = ((array_xyxy[:, 1] + array_xyxy[:, 3]) / 2).astype(int)  # center y
            cell_info[:, 2] = ((array_xyxy[:, 2] - array_xyxy[:, 0]) + (array_xyxy[:, 3] - array_xyxy[:, 1])) / 4  # 半径
            cell_info[:, 3] = np.ones(len(det))  # 团属性
            cell_info[:, 4] = det[:, -1].astype(int)  # 标签
            cell_info[:, 5] = ((array_xyxy[:, 2] - array_xyxy[:, 0]) + (array_xyxy[:, 3] - array_xyxy[:, 1])) / 2  # 直径
            cell_info[:, 6] = array_xyxy[:, 0]  # tx
            cell_info[:, 7] = array_xyxy[:, 1]  # ty
            cell_info[:, 8] = array_xyxy[:, 2]  # bx
            cell_info[:, 9] = array_xyxy[:, 3]  # by
            cell_info[:, 10] = np.multiply((array_xyxy[:, 2] - array_xyxy[:, 0]),
                                           (array_xyxy[:, 3] - array_xyxy[:, 1]))  # 面积
            cell_info[:, 11] = np.maximum((array_xyxy[:, 2] - array_xyxy[:, 0]),
                                          (array_xyxy[:, 3] - array_xyxy[:, 1]))  # 长轴
            cell_info[:, 12] = np.minimum((array_xyxy[:, 2] - array_xyxy[:, 0]),
                                          (array_xyxy[:, 3] - array_xyxy[:, 1]))  # 短轴
            log.info(durations_in_seconds)
        return cell_info

    def computer_black(self,roi):
        if len(roi.shape) > 2:
            gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
            mask_flag = False
        else:
            gray = roi
            mask_flag = True
        height, width = roi.shape[:2]
        center_x = width // 2
        center_y = height // 2
        if cv2.countNonZero(gray) != 0:
            if gray[int(center_y), int(center_x)] < 1:
                return True
            else:
                gray_sum = gray.sum()
                if mask_flag:
                    if gray_sum < cv2.countNonZero(gray):
                        return True
                else:
                    if gray_sum < 10:
                        return True
                return False
        else:
            return True
    def read_image(self,input_im_path,channel="BF"):
        if not os.path.exists(input_im_path):
            log.info(f"{input_im_path} not exist")
            return None,None,None
        jyh_img = cv2.imdecode(np.fromfile(input_im_path, dtype=np.uint8), cv2.IMREAD_COLOR)
        try:
            if "_cut.jpg" in input_im_path:
                input_im = cv2.imdecode(np.fromfile(input_im_path[:-8] + "_merge_src.jpg", dtype=np.uint8),
                                   cv2.IMREAD_COLOR)
            else:
                input_im = cv2.imdecode(np.fromfile(input_im_path[:-4] + "_src.jpg", dtype=np.uint8),
                                   cv2.IMREAD_COLOR)
        except:
            input_im = jyh_img
            log.info("不存在原图,使用均一化图")
        crop_mask = np.ones((input_im.shape[0], input_im.shape[1]), dtype=np.uint8)
        if channel == "BF":
            try:
                CutoutConfig = fr"{os.sep.join(os.path.dirname(input_im_path).split(os.sep)[:-1])}\CutoutConfig.txt"
                if os.path.exists(
                        CutoutConfig.replace('D:', 'E:').replace('d:', 'e:').replace('imageTemp', 'imagesource')):
                    CutoutConfig = CutoutConfig.replace('D:', 'E:').replace('d:', 'e:').replace('imageTemp',
                                                                                                'imagesource')
                log.info(f"检查CutoutConfig.txt {CutoutConfig.replace('imageTemp', 'imagesource')}")
                if os.path.exists(CutoutConfig.replace('imageTemp', 'imagesource')):
                    with open(CutoutConfig.replace('imageTemp', 'imagesource'), "r") as f:
                        cut_info = f.read().split(",")
                else:
                    log.error("不存在CutoutConfig.txt")
                    cut_info = self.cut_info.split(",")
                log.info(f"cut info {cut_info}")
                crop_mask = crop_hole(input_im, kong_num=cut_info[0], lens=cut_info[1], view_judge=int(cut_info[2]))
            except Exception as e:
                log.error(f"crop hole fail {e}")
        return jyh_img, input_im, crop_mask
    def detction(self,
                 input_im_path,
                 image_save_path,
                 labelme_path,
                 data_save_path,
                 fcs_save_path,
                 cut_size: int = 640,
                 BF_conf_thrd=0.1,
                 BF_iou_thrd=0.1,
                 FL1_conf_thrd=0.1,
                 FL1_iou_thrd=0.1,
                 FL2_conf_thrd=0.1,
                 FL2_iou_thrd=0.1,
                 FL3_conf_thrd=0.1,
                 FL3_iou_thrd=0.1,
                 FL4_conf_thrd=0.1,
                 FL4_iou_thrd=0.1):
        log.info(f"labelme_path {labelme_path} cut_size:{cut_size} BF_conf_thrd:{BF_conf_thrd} BF_iou_thrd:{BF_iou_thrd} FL1_conf_thrd:{FL1_conf_thrd} FL1_iou_thrd:{FL1_iou_thrd} FL2_conf_thrd:{FL2_conf_thrd} FL2_iou_thrd:{FL2_iou_thrd} FL3_conf_thrd:{FL3_conf_thrd} FL3_iou_thrd:{FL3_iou_thrd} FL4_conf_thrd:{FL4_conf_thrd} FL4_iou_thrd:{FL4_iou_thrd}")
        origin_shape = crop_mask = None
        bf0copy = fl1copy = fl2copy = fl3copy = fl4copy = None
        bf0_cell_infos = fl1_cell_infos = fl2_cell_infos = fl3_cell_infos = fl4_cell_infos = None
        try:
            log.info("BF process start")
            bf0_jyh_img, bf0, bf_crop_mask = self.read_image(input_im_path[0], channel="BF")
            if bf0 is not None:
                bf0_cell_infos = cell_list()
                origin_shape = bf0.shape
                crop_mask = bf_crop_mask
                bf0copy = bf0.copy()
                bf_labelme = labelme_path[0]
                bf0_cell_info = np.empty([0, 13])
                if os.path.exists(bf_labelme):
                    try:
                        log.info(f"use {bf_labelme} result computer info")
                        with open(bf_labelme, "r") as f:
                            labelme_json = json.load(f)
                        shapes = labelme_json["shapes"]
                        array_xyxy = np.empty([len(shapes), 6])
                        for i in range(len(shapes)):
                            score = 1.0
                            if labelme_json["shapes"][i]["shape_type"] == "polygon":
                                points = labelme_json["shapes"][i]["points"]
                                xs = [p[0] for p in points]
                                ys = [p[1] for p in points]
                                minx = min(xs)
                                maxx = max(xs)
                                miny = min(ys)
                                maxy = max(ys)
                                # 补充下面的代码实现添加shape的外部矩形到cell_info
                                try:
                                    class_id = int(labelme_json["shapes"][i]["label"])
                                except Exception as e:
                                    class_id = 0
                                array_xyxy[i] = np.array([minx, miny, maxx, maxy, score, class_id])
                        bf0_cell_info = np.empty([len(shapes), 13])
                        bf0_cell_info[:, 0] = ((array_xyxy[:, 0] + array_xyxy[:, 2]) / 2).astype(int)  # center x
                        bf0_cell_info[:, 1] = ((array_xyxy[:, 1] + array_xyxy[:, 3]) / 2).astype(int)  # center y
                        bf0_cell_info[:, 2] = ((array_xyxy[:, 2] - array_xyxy[:, 0]) + (
                                    array_xyxy[:, 3] - array_xyxy[:, 1])) / 4  # 半径
                        bf0_cell_info[:, 3] = np.ones(len(shapes))  # 团属性
                        bf0_cell_info[:, 4] = array_xyxy[:, -1]  # 标签
                        bf0_cell_info[:, 5] = ((array_xyxy[:, 2] - array_xyxy[:, 0]) + (
                                    array_xyxy[:, 3] - array_xyxy[:, 1])) / 2  # 直径
                        bf0_cell_info[:, 6] = array_xyxy[:, 0]  # tx
                        bf0_cell_info[:, 7] = array_xyxy[:, 1]  # ty
                        bf0_cell_info[:, 8] = array_xyxy[:, 2]  # bx
                        bf0_cell_info[:, 9] = array_xyxy[:, 3]  # by
                        bf0_cell_info[:, 10] = np.multiply((array_xyxy[:, 2] - array_xyxy[:, 0]),
                                                       (array_xyxy[:, 3] - array_xyxy[:, 1]))  # 面积
                        bf0_cell_info[:, 11] = np.maximum((array_xyxy[:, 2] - array_xyxy[:, 0]),
                                                      (array_xyxy[:, 3] - array_xyxy[:, 1]))  # 长轴
                        bf0_cell_info[:, 12] = np.minimum((array_xyxy[:, 2] - array_xyxy[:, 0]),
                                                      (array_xyxy[:, 3] - array_xyxy[:, 1]))  # 短轴
                    except Exception as e:
                        error_msg = traceback.format_exc()
                        log.error(f"labelme_json error {error_msg}")
                else:
                    bf0_cell_info = self.sice_detection(bf0, cut_size, "BF", BF_conf_thrd, BF_iou_thrd)
                try:
                    self.send_response("MsgTime", self.msg_id, {"tid": self.task_id, "Time": int(bf0_cell_info.shape[0] * 0.01) + 600})
                except Exception as e:
                    log.error(f"send MsgTime error:{e}")
                for cell_num, (cellx, celly, radius, group_type, label_num, diam, xyxy_0, xyxy_1, xyxy_2, xyxy_3, Area, max_aix,min_aix) in enumerate(bf0_cell_info):
                    if self.computer_black(crop_mask[int(xyxy_1):int(xyxy_3), int(xyxy_0):int(xyxy_2)]):
                        continue
                    cell = cell_struct(BF=channel_struct(group_type=group_type, local_channel=0,
                                                         type=self.shuxing[self.SumLabel.index("BF")]).get_det_info(img=bf0,
                                                                                                          xyxy_0=int(
                                                                                                              xyxy_0),
                                                                                                          xyxy_1=int(
                                                                                                              xyxy_1),
                                                                                                          xyxy_2=int(
                                                                                                              xyxy_2),
                                                                                                          xyxy_3=int(
                                                                                                              xyxy_3)))
                    bf0_cell_infos.push_cell(cell)
                log.info(f"bf0_cell_infos info Number of Cell:{bf0_cell_infos.get_length()}")
                bf0_cell_infos.group_computer(0)
                bf0 = bf0_cell_infos.draw_group(bf0_jyh_img, 0)
                im0 = bf0_cell_infos.draw_cell_info(bf0, 0)
                save_resultimage(im0, image_save_path[0])
                log.info("BF process success")
        except Exception as e:
            error_message = traceback.format_exc()
            log.error(f"BF detect fail {error_message}")
        try:
            log.info("FL1 process start")
            fl10_jyh_img, fl10, fl1_crop_mask = self.read_image(input_im_path[1])
            if fl10 is not None:
                fl1_cell_infos = cell_list()
                if crop_mask is None:
                    crop_mask = fl1_crop_mask
                origin_shape = fl10.shape
                fl1copy = fl10.copy()
                fl1_labelme = labelme_path[1]
                fl10_cell_info = np.empty([0, 13])
                if os.path.exists(fl1_labelme):
                    try:
                        log.info(f"use {fl1_labelme} result computer info")
                        with open(fl1_labelme, "r") as f:
                            labelme_json = json.load(f)
                        shapes = labelme_json["shapes"]
                        array_xyxy = np.empty([len(shapes), 6])
                        for i in range(len(shapes)):
                            score = 1.0
                            if labelme_json["shapes"][i]["shape_type"] == "polygon":
                                points = labelme_json["shapes"][i]["points"]
                                xs = [p[0] for p in points]
                                ys = [p[1] for p in points]
                                minx = min(xs)
                                maxx = max(xs)
                                miny = min(ys)
                                maxy = max(ys)
                                # 补充下面的代码实现添加shape的外部矩形到cell_info
                                try:
                                    class_id = int(labelme_json["shapes"][i]["label"])
                                except Exception as e:
                                    class_id = 0
                                array_xyxy[i] = np.array([minx, miny, maxx, maxy, score, class_id])
                        fl10_cell_info = np.empty([len(shapes), 13])
                        fl10_cell_info[:, 0] = ((array_xyxy[:, 0] + array_xyxy[:, 2]) / 2).astype(int)  # center x
                        fl10_cell_info[:, 1] = ((array_xyxy[:, 1] + array_xyxy[:, 3]) / 2).astype(int)  # center y
                        fl10_cell_info[:, 2] = ((array_xyxy[:, 2] - array_xyxy[:, 0]) + (
                                    array_xyxy[:, 3] - array_xyxy[:, 1])) / 4  # 半径
                        fl10_cell_info[:, 3] = np.ones(len(shapes))  # 团属性
                        fl10_cell_info[:, 4] = array_xyxy[:, -1]  # 标签
                        fl10_cell_info[:, 5] = ((array_xyxy[:, 2] - array_xyxy[:, 0]) + (
                                    array_xyxy[:, 3] - array_xyxy[:, 1])) / 2  # 直径
                        fl10_cell_info[:, 6] = array_xyxy[:, 0]  # tx
                        fl10_cell_info[:, 7] = array_xyxy[:, 1]  # ty
                        fl10_cell_info[:, 8] = array_xyxy[:, 2]  # bx
                        fl10_cell_info[:, 9] = array_xyxy[:, 3]  # by
                        fl10_cell_info[:, 10] = np.multiply((array_xyxy[:, 2] - array_xyxy[:, 0]),
                                                       (array_xyxy[:, 3] - array_xyxy[:, 1]))  # 面积
                        fl10_cell_info[:, 11] = np.maximum((array_xyxy[:, 2] - array_xyxy[:, 0]),
                                                      (array_xyxy[:, 3] - array_xyxy[:, 1]))  # 长轴
                        fl10_cell_info[:, 12] = np.minimum((array_xyxy[:, 2] - array_xyxy[:, 0]),
                                                      (array_xyxy[:, 3] - array_xyxy[:, 1]))  # 短轴
                    except Exception as e:
                        error_msg = traceback.format_exc()
                        log.error(f"labelme_json error {error_msg}")
                else:
                    fl10_cell_info = self.sice_detection(fl10, cut_size, "FL", FL1_conf_thrd, FL1_iou_thrd)
                try:
                    self.send_response("MsgTime", self.msg_id,{"tid": self.task_id, "Time": int(fl10_cell_info.shape[0] * 0.01) + 600})
                except Exception as e:
                    log.error(f"send MsgTime error:{e}")
                for cell_num, (
                        cellx, celly, radius, group_type, label_num, diam, xyxy_0, xyxy_1, xyxy_2, xyxy_3, Area,
                        max_aix,
                        min_aix) in enumerate(fl10_cell_info):
                    if self.computer_black(crop_mask[int(xyxy_1):int(xyxy_3), int(xyxy_0):int(xyxy_2)]):
                        continue
                    cell = cell_struct(FL1=channel_struct(group_type=group_type, local_channel=1,
                                                          type=self.shuxing[
                                                              self.SumLabel.index("FL1")]).get_det_info(
                        img=fl10, xyxy_0=int(xyxy_0), xyxy_1=int(xyxy_1), xyxy_2=int(xyxy_2), xyxy_3=int(xyxy_3)))
                    fl1_cell_infos.push_cell(cell)
                log.info(f"fl1_cell_infos info Number of Cell:{fl1_cell_infos.get_length()}")
                fl1_cell_infos.group_computer(1)
                fl10 = fl1_cell_infos.draw_group(fl10_jyh_img, 1)
                im0 = fl1_cell_infos.draw_cell_info(fl10, 1)
                save_resultimage(im0, image_save_path[1])
                log.info("FL1 process success")
        except Exception as e:
            error_message = traceback.format_exc()
            log.error(f"FL1 detect fail {error_message}")
        try:
            log.info("FL2 process start")
            fl20_jyh_img, fl20, fl2_crop_mask = self.read_image(input_im_path[2])
            if fl20 is not None:
                fl2_cell_infos = cell_list()
                if crop_mask is None:
                    crop_mask = fl2_crop_mask
                origin_shape = fl20.shape
                fl2copy = fl20.copy()
                fl2_labelme = labelme_path[2]
                fl20_cell_info = np.empty([0, 13])
                if os.path.exists(fl2_labelme):
                    try:
                        log.info(f"use {fl2_labelme} result computer info")
                        with open(fl2_labelme, "r") as f:
                            labelme_json = json.load(f)
                        shapes = labelme_json["shapes"]
                        array_xyxy = np.empty([len(shapes), 6])
                        for i in range(len(shapes)):
                            score = 1.0
                            if labelme_json["shapes"][i]["shape_type"] == "polygon":
                                points = labelme_json["shapes"][i]["points"]
                                xs = [p[0] for p in points]
                                ys = [p[1] for p in points]
                                minx = min(xs)
                                maxx = max(xs)
                                miny = min(ys)
                                maxy = max(ys)
                                # 补充下面的代码实现添加shape的外部矩形到cell_info
                                try:
                                    class_id = int(labelme_json["shapes"][i]["label"])
                                except Exception as e:
                                    class_id = 0
                                array_xyxy[i] = np.array([minx, miny, maxx, maxy, score, class_id])
                        fl20_cell_info = np.empty([len(shapes), 13])
                        fl20_cell_info[:, 0] = ((array_xyxy[:, 0] + array_xyxy[:, 2]) / 2).astype(int)  # center x
                        fl20_cell_info[:, 1] = ((array_xyxy[:, 1] + array_xyxy[:, 3]) / 2).astype(int)  # center y
                        fl20_cell_info[:, 2] = ((array_xyxy[:, 2] - array_xyxy[:, 0]) + (
                                    array_xyxy[:, 3] - array_xyxy[:, 1])) / 4  # 半径
                        fl20_cell_info[:, 3] = np.ones(len(shapes))  # 团属性
                        fl20_cell_info[:, 4] = array_xyxy[:, -1]  # 标签
                        fl20_cell_info[:, 5] = ((array_xyxy[:, 2] - array_xyxy[:, 0]) + (
                                    array_xyxy[:, 3] - array_xyxy[:, 1])) / 2  # 直径
                        fl20_cell_info[:, 6] = array_xyxy[:, 0]  # tx
                        fl20_cell_info[:, 7] = array_xyxy[:, 1]  # ty
                        fl20_cell_info[:, 8] = array_xyxy[:, 2]  # bx
                        fl20_cell_info[:, 9] = array_xyxy[:, 3]  # by
                        fl20_cell_info[:, 10] = np.multiply((array_xyxy[:, 2] - array_xyxy[:, 0]),
                                                       (array_xyxy[:, 3] - array_xyxy[:, 1]))  # 面积
                        fl20_cell_info[:, 11] = np.maximum((array_xyxy[:, 2] - array_xyxy[:, 0]),
                                                      (array_xyxy[:, 3] - array_xyxy[:, 1]))  # 长轴
                        fl20_cell_info[:, 12] = np.minimum((array_xyxy[:, 2] - array_xyxy[:, 0]),
                                                      (array_xyxy[:, 3] - array_xyxy[:, 1]))  # 短轴
                    except Exception as e:
                        error_msg = traceback.format_exc()
                        log.error(f"labelme_json error {error_msg}")
                else:
                    fl20_cell_info = self.sice_detection(fl20, cut_size, "FL", FL2_conf_thrd, FL2_iou_thrd)
                try:
                    self.send_response("MsgTime", self.msg_id,{"tid": self.task_id, "Time": int(fl20_cell_info.shape[0] * 0.01) + 600})
                except Exception as e:
                    log.error(f"send MsgTime error:{e}")
                for cell_num, (
                        cellx, celly, radius, group_type, label_num, diam, xyxy_0, xyxy_1, xyxy_2, xyxy_3, Area, max_aix,
                        min_aix) in enumerate(fl20_cell_info):
                    if self.computer_black(crop_mask[int(xyxy_1):int(xyxy_3), int(xyxy_0):int(xyxy_2)]):
                        continue
                    cell = cell_struct(FL2=channel_struct(group_type=group_type, local_channel=2,
                                                          type=self.shuxing[
                                                              self.SumLabel.index("FL2")]).get_det_info(
                        img=fl20, xyxy_0=int(xyxy_0), xyxy_1=int(xyxy_1), xyxy_2=int(xyxy_2), xyxy_3=int(xyxy_3)))
                    fl2_cell_infos.push_cell(cell)
                log.info(f"fl2_cell_infos info Number of Cell:{fl2_cell_infos.get_length()}")
                fl2_cell_infos.group_computer(2)
                fl20 = fl2_cell_infos.draw_group(fl20_jyh_img, 2)
                im0 = fl2_cell_infos.draw_cell_info(fl20, 2)
                save_resultimage(im0, image_save_path[2])
                log.info("FL2 process success")
        except Exception as e:
            error_message = traceback.format_exc()
            log.error(f"FL2 detect fail {error_message}")
        try:
            log.info("FL3 process start")
            fl30_jyh_img, fl30, fl3_crop_mask = self.read_image(input_im_path[3])
            if fl30 is not None:
                fl3_cell_infos = cell_list()
                if crop_mask is None:
                    crop_mask = fl3_crop_mask
                origin_shape = fl30.shape
                fl3copy = fl30.copy()
                fl3_labelme = labelme_path[3]
                fl30_cell_info = np.empty([0, 13])
                if os.path.exists(fl3_labelme):
                    try:
                        log.info(f"use {fl3_labelme} result computer info")
                        with open(fl3_labelme, "r") as f:
                            labelme_json = json.load(f)
                        shapes = labelme_json["shapes"]
                        array_xyxy = np.empty([len(shapes), 6])
                        for i in range(len(shapes)):
                            score = 1.0
                            if labelme_json["shapes"][i]["shape_type"] == "polygon":
                                points = labelme_json["shapes"][i]["points"]
                                xs = [p[0] for p in points]
                                ys = [p[1] for p in points]
                                minx = min(xs)
                                maxx = max(xs)
                                miny = min(ys)
                                maxy = max(ys)
                                # 补充下面的代码实现添加shape的外部矩形到cell_info
                                try:
                                    class_id = int(labelme_json["shapes"][i]["label"])
                                except Exception as e:
                                    class_id = 0
                                array_xyxy[i] = np.array([minx, miny, maxx, maxy, score, class_id])
                        fl30_cell_info = np.empty([len(shapes), 13])
                        fl30_cell_info[:, 0] = ((array_xyxy[:, 0] + array_xyxy[:, 2]) / 2).astype(int)  # center x
                        fl30_cell_info[:, 1] = ((array_xyxy[:, 1] + array_xyxy[:, 3]) / 2).astype(int)  # center y
                        fl30_cell_info[:, 2] = ((array_xyxy[:, 2] - array_xyxy[:, 0]) + (array_xyxy[:, 3] - array_xyxy[:, 1])) / 4  # 半径
                        fl30_cell_info[:, 3] = np.ones(len(shapes))  # 团属性
                        fl30_cell_info[:, 4] = array_xyxy[:, -1]  # 标签
                        fl30_cell_info[:, 5] = ((array_xyxy[:, 2] - array_xyxy[:, 0]) + (array_xyxy[:, 3] - array_xyxy[:, 1])) / 2  # 直径
                        fl30_cell_info[:, 6] = array_xyxy[:, 0]  # tx
                        fl30_cell_info[:, 7] = array_xyxy[:, 1]  # ty
                        fl30_cell_info[:, 8] = array_xyxy[:, 2]  # bx
                        fl30_cell_info[:, 9] = array_xyxy[:, 3]  # by
                        fl30_cell_info[:, 10] = np.multiply((array_xyxy[:, 2] - array_xyxy[:, 0]),
                                                            (array_xyxy[:, 3] - array_xyxy[:, 1]))  # 面积
                        fl30_cell_info[:, 11] = np.maximum((array_xyxy[:, 2] - array_xyxy[:, 0]),
                                                           (array_xyxy[:, 3] - array_xyxy[:, 1]))  # 长轴
                        fl30_cell_info[:, 12] = np.minimum((array_xyxy[:, 2] - array_xyxy[:, 0]),
                                                           (array_xyxy[:, 3] - array_xyxy[:, 1]))  # 短轴
                    except Exception as e:
                        error_msg = traceback.format_exc()
                        log.error(f"labelme_json error {error_msg}")
                else:
                    fl30_cell_info = self.sice_detection(self.RemoveSaturation(fl30), cut_size, "FL", FL3_conf_thrd,
                                                FL3_iou_thrd)
                try:
                    self.send_response("MsgTime", self.msg_id,{"tid": self.task_id, "Time": int(fl30_cell_info.shape[0] * 0.01) + 600})
                except Exception as e:
                    log.error(f"send MsgTime error:{e}")
                for cell_num, (
                        cellx, celly, radius, group_type, label_num, diam, xyxy_0, xyxy_1, xyxy_2, xyxy_3, Area, max_aix,
                        min_aix) in enumerate(fl30_cell_info):
                    if self.computer_black(crop_mask[int(xyxy_1):int(xyxy_3), int(xyxy_0):int(xyxy_2)]):
                        continue
                    cell = cell_struct(FL3=channel_struct(group_type=group_type, local_channel=3,
                                                          type=self.shuxing[
                                                              self.SumLabel.index("FL3")]).get_det_info(
                        img=fl30, xyxy_0=int(xyxy_0), xyxy_1=int(xyxy_1), xyxy_2=int(xyxy_2), xyxy_3=int(xyxy_3)))
                    fl3_cell_infos.push_cell(cell)
                log.info(f"fl3_cell_infos info Number of Cell:{fl3_cell_infos.get_length()}")
                fl3_cell_infos.group_computer(3)
                fl30 = fl3_cell_infos.draw_group(fl30_jyh_img, 3)
                im0 = fl3_cell_infos.draw_cell_info(fl30, 3)
                save_resultimage(im0, image_save_path[3])
                log.info("FL3 process success")
        except Exception as e:
            error_message = traceback.format_exc()
            log.error(f"FL3 detect fail {error_message}")
        try:
            log.info("FL4 process start")
            fl40_jyh_img, fl40, fl4_crop_mask = self.read_image(input_im_path[4])
            if fl40 is not None:
                fl4_cell_infos = cell_list()
                if crop_mask is None:
                    crop_mask = fl4_crop_mask
                origin_shape = fl40.shape
                fl4copy = fl40.copy()
                fl4_labelme = labelme_path[3]
                fl40_cell_info = np.empty([0, 13])
                if os.path.exists(fl4_labelme):
                    try:
                        log.info(f"use {fl4_labelme} result computer info")
                        with open(fl4_labelme, "r") as f:
                            labelme_json = json.load(f)
                        shapes = labelme_json["shapes"]
                        array_xyxy = np.empty([len(shapes), 6])
                        for i in range(len(shapes)):
                            score = 1.0
                            if labelme_json["shapes"][i]["shape_type"] == "polygon":
                                points = labelme_json["shapes"][i]["points"]
                                xs = [p[0] for p in points]
                                ys = [p[1] for p in points]
                                minx = min(xs)
                                maxx = max(xs)
                                miny = min(ys)
                                maxy = max(ys)
                                # 补充下面的代码实现添加shape的外部矩形到cell_info
                                try:
                                    class_id = int(labelme_json["shapes"][i]["label"])
                                except Exception as e:
                                    class_id = 0
                                array_xyxy[i] = np.array([minx, miny, maxx, maxy, score, class_id])
                        fl40_cell_info = np.empty([len(shapes), 13])
                        fl40_cell_info[:, 0] = ((array_xyxy[:, 0] + array_xyxy[:, 2]) / 2).astype(int)  # center x
                        fl40_cell_info[:, 1] = ((array_xyxy[:, 1] + array_xyxy[:, 3]) / 2).astype(int)  # center y
                        fl40_cell_info[:, 2] = ((array_xyxy[:, 2] - array_xyxy[:, 0]) + (
                                    array_xyxy[:, 3] - array_xyxy[:, 1])) / 4  # 半径
                        fl40_cell_info[:, 3] = np.ones(len(shapes))  # 团属性
                        fl40_cell_info[:, 4] = array_xyxy[:, -1]  # 标签
                        fl40_cell_info[:, 5] = ((array_xyxy[:, 2] - array_xyxy[:, 0]) + (
                                    array_xyxy[:, 3] - array_xyxy[:, 1])) / 2  # 直径
                        fl40_cell_info[:, 6] = array_xyxy[:, 0]  # tx
                        fl40_cell_info[:, 7] = array_xyxy[:, 1]  # ty
                        fl40_cell_info[:, 8] = array_xyxy[:, 2]  # bx
                        fl40_cell_info[:, 9] = array_xyxy[:, 3]  # by
                        fl40_cell_info[:, 10] = np.multiply((array_xyxy[:, 2] - array_xyxy[:, 0]),
                                                            (array_xyxy[:, 3] - array_xyxy[:, 1]))  # 面积
                        fl40_cell_info[:, 11] = np.maximum((array_xyxy[:, 2] - array_xyxy[:, 0]),
                                                           (array_xyxy[:, 3] - array_xyxy[:, 1]))  # 长轴
                        fl40_cell_info[:, 12] = np.minimum((array_xyxy[:, 2] - array_xyxy[:, 0]),
                                                           (array_xyxy[:, 3] - array_xyxy[:, 1]))  # 短轴
                    except Exception as e:
                        error_msg = traceback.format_exc()
                        log.error(f"labelme_json error {error_msg}")
                else:
                    fl40_cell_info = self.sice_detection(self.RemoveSaturation(fl40), cut_size, "FL", FL4_conf_thrd,
                                                    FL4_iou_thrd)
                try:
                    self.send_response("MsgTime", self.msg_id,{"tid": self.task_id, "Time": int(fl40_cell_info.shape[0] * 0.01) + 600})
                except Exception as e:
                    log.error(f"send MsgTime error:{e}")
                for cell_num, (
                        cellx, celly, radius, group_type, label_num, diam, xyxy_0, xyxy_1, xyxy_2, xyxy_3, Area, max_aix,
                        min_aix) in enumerate(fl40_cell_info):
                    if self.computer_black(crop_mask[int(xyxy_1):int(xyxy_3), int(xyxy_0):int(xyxy_2)]):
                        continue
                    cell = cell_struct(FL4=channel_struct(group_type=group_type, local_channel=4,
                                                          type=self.shuxing[self.SumLabel.index("FL4")]).get_det_info(img=fl40,
                                                                                                            xyxy_0=int(
                                                                                                                xyxy_0),
                                                                                                            xyxy_1=int(
                                                                                                                xyxy_1),
                                                                                                            xyxy_2=int(
                                                                                                                xyxy_2),
                                                                                                            xyxy_3=int(
                                                                                                                xyxy_3)))
                    fl4_cell_infos.push_cell(cell)
                log.info(f"fl4_cell_infos info Number of Cell:{fl4_cell_infos.get_length()}")
                fl4_cell_infos.group_computer(4)
                fl40 = fl4_cell_infos.draw_group(fl40_jyh_img, 4)
                im0 = fl4_cell_infos.draw_cell_info(fl40, 4)
                save_resultimage(im0, image_save_path[4])
                log.info("FL4 process success")
        except Exception as e:
            error_message = traceback.format_exc()
            log.error(f"FL4 detect fail {error_message}")
        try:
            cell_infos = cell_list()
            for cell_info in [bf0_cell_infos, fl1_cell_infos, fl2_cell_infos, fl3_cell_infos, fl4_cell_infos]:
                if cell_info is not None:
                    cell_infos.merge_cell_list(cell_info)
            fl_merge_image = np.zeros(origin_shape, dtype=np.uint8)
            for img in [fl1copy, fl2copy, fl3copy, fl4copy]:
                if img is not None:
                    fl_merge_image = cv2.add(fl_merge_image, img)
            try:
                merge_image = cv2.add(fl_merge_image.copy(), bf0copy)
            except Exception as e:
                merge_image = bf0copy
                log.error(f"merge fail use bf image {e}")
            log.info(f"cell_number {cell_infos.get_length()}")
            save_resultimage(fl_merge_image, image_save_path[5][:-4] + "_SrcFL.jpg")
            cell_infos.draw_cell_info(fl_merge_image, "all")
            save_resultimage(fl_merge_image, image_save_path[5][:-4] + "_FL.jpg")
            save_resultimage(merge_image, image_save_path[5][:-4] + "_SrcAll.jpg")
            cell_infos.draw_cell_info(merge_image, "all")
            save_resultimage(merge_image, image_save_path[5])
            cell_infos.get_shuxing5(origin_shape)
            cell_infos.to_csv(data_save_path, group_computer=False)
            fcs_infos = cell_infos.get_shuxing5(origin_shape, bf_img=bf0copy, fl1_img=fl1copy, fl2_img=fl2copy,
                                                fl3_img=fl3copy, fl4_img=fl4copy, fcs_output=True)
            fcs_infos.to_csv(fcs_save_path, group_computer=False)
        except Exception as e:
            error_message = traceback.format_exc()
            log.error(f"merge cell_infos fail {error_message}")
        log.info("处理结束")


if __name__ == '__main__':
    model = AOPImergeInferengine(
        bf_model_file_path=r"AOPI_merge_APP/xuanfu_BF_single_label0725_agg_fintune_xuanfu_BF_single_label0722_agg_yolov5s/best.engine",
        fl_model_file_path=r"AOPI_merge_APP/xuanfu_FL0725_agg_yolov5s/best.engine")
    # input_im_path = [r"H:\suanfa_test\540\images\B2_01_BF_merge_src.jpg",
    #                  r"H:\suanfa_test\540\images\B2_01_FL1_merge.jpg",
    #                  r"H:\suanfa_test\540\images\B2_01_FL2_merge.jpg",
    #                  r""]
    # image_save_path = [r"H:\A3_01_01_01_BF.jpg", r"H:\A3_01_01_01_FL1.jpg",
    #                    r"H:\A3_01_01_01_FL2.jpg", r"H:\A3_01_01_01_FL3.jpg",
    #                    r"H:\A3_01_01_01_FL4.jpg", r"H:\A3_01_01_01_merge.jpg"]
    # cut_size= 640
    # FL1_conf_thrd = 0.1
    # FL1_iou_thrd = 0.1
    # FL2_conf_thrd = 0.1
    # FL2_iou_thrd = 0.1
    # FL3_conf_thrd = 0.1
    # FL3_iou_thrd = 0.1
    # FL4_conf_thrd = 0.1
    # FL4_iou_thrd = 0.1
    # data_save_path = r"H:\A3_01_01_01_BF.csv"
    # fcs_save_path = r"H:\A3_01_01_01_BF_FCS.csv"
    # model.detction(input_im_path=input_im_path, image_save_path=image_save_path,data_save_path=data_save_path,fcs_save_path=fcs_save_path,
    #                cut_size=cut_size,BF_conf_thrd=0.1,BF_iou_thrd=0.1,FL1_conf_thrd=0.1,FL1_iou_thrd=0.1,FL2_conf_thrd=0.1,FL2_iou_thrd=0.1,FL3_conf_thrd=0.1,FL3_iou_thrd=0.1,FL4_conf_thrd=0.1,FL4_iou_thrd=0.1
    #                )
    from imutils.paths import list_images
    for i in list_images(r"H:\159\images"):
        if "_result" in i:
            continue
        if "BF_merge.jpg" not in i:
            continue
        input_im_path = [i,
                         i.replace("BF", "FL1"),
                         i.replace("BF", "FL2"),
                         i.replace("BF", "FL3"),]
        image_save_path = [i[:-4]+"_result.jpg", i.replace("BF", "FL1")[:-4]+"_result.jpg",
                           i.replace("BF", "FL2")[:-4]+"_result.jpg", i.replace("BF", "FL3")[:-4]+"_result.jpg",
                           r"H:\A3_01_01_01_FL4.jpg", i[:-4]+"_merge_result.jpg"]
        cut_size= 640
        BF_conf_thrd = 0.1
        BF_iou_thrd = 0.1
        FL1_conf_thrd = 0.1
        FL1_iou_thrd = 0.1
        FL2_conf_thrd = 0.1
        FL2_iou_thrd = 0.1
        FL3_conf_thrd = 0.1
        FL3_iou_thrd = 0.1
        FL4_conf_thrd = 0.1
        FL4_iou_thrd = 0.1
        data_save_path = i[:-4]+"_result.csv"
        fcs_save_path = i[:-4]+"_result_fcs.csv"
        model.detction(input_im_path=input_im_path, image_save_path=image_save_path,data_save_path=data_save_path,fcs_save_path=fcs_save_path,
                       cut_size=cut_size,BF_conf_thrd=BF_conf_thrd,BF_iou_thrd=BF_iou_thrd,FL1_conf_thrd=FL1_conf_thrd,FL1_iou_thrd=FL1_iou_thrd,FL2_conf_thrd=FL2_conf_thrd,FL2_iou_thrd=FL2_iou_thrd,FL3_conf_thrd=FL3_conf_thrd,FL3_iou_thrd=FL3_iou_thrd,FL4_conf_thrd=FL4_conf_thrd,FL4_iou_thrd=FL4_iou_thrd
                       ,labelme_path=["", "", "", "", "", ""])
