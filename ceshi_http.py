import json

import requests

root_url="http://localhost:12318"
def add_task(num=1):
    for i in range(num):
        get_task_url=f"{root_url}/mq/msg/add?type=processing"
        # data1={"exp_type": "single_clone", "input_image_path": "H:\\单图图库\\d3C3_01.jpg", "image_save_path": "H:\\test\\B2_01_res_B_origin_AIa.jpg", "data_save_path": "H:\\test\\B2_01_res_B_origin_AIa.csv", "conf_thrd": 0.5, "iou_thrd": 0.1, "input_size": 1280}
        # data1={"exp_type":"confluence","input_image_path":r"H:\单图图库\汇合度\1\H12_01_cut.jpg","image_save_path":r"H:\test\H11_01_count_BF_AI.jpg","data_save_path":r"H:\test\H11_01_count_BF_AI.csv","cut_patch":3,"fcs_save_path":r"H:\test\A3_01_01_01_Trans.csv","fast_mode":True}
        # data1={"input_image_path": r"H:\单图图库\台盼蓝\A1_01_01_01.jpg", "exp_type": "trypanblue","image_save_path":r"H:\test\A1_01_01_01_AI.jpg","data_save_path":r"H:\test\A1_01_01_01_AI.csv","conf_thrd":0.4,"iou_thrd":0.1,"fcs_save_path":r"H:\test\A1_01_01_01_AI.csv","cut_info":"25,4,1"}
        # data1={"exp_type": "hepatoma", "input_image_path": "H:\\单图图库\\d3C3_01.jpg", "image_save_path": "H:\\test\\B2_01_res_B_origin_AIa.jpg", "data_save_path": "H:\\test\\B2_01_res_B_origin_AIa.csv", "conf_thrd": 0.5, "iou_thrd": 0.1, "input_size": 1280}
        # data1={"exp_type": "single_clone", "input_image_path": "H:\\单图图库\\d3C3_01.jpg", "image_save_path": "H:\\test\\B2_01_res_B_origin_AIa.jpg", "data_save_path": "H:\\test\\B2_01_res_B_origin_AIa.csv", "conf_thrd": 0.5, "iou_thrd": 0.1, "input_size": 1280}
        data1 = {"exp_type": "suspension_BF", "input_image_path": "H:\\单图图库\\d3C3_01.jpg",
                 "image_save_path": "H:\\test\\B2_01_res_B_origin_AIa.jpg",
                 "data_save_path": "H:\\test\\B2_01_res_B_origin_AIa.csv", "conf_thrd": 0.5, "iou_thrd": 0.1,
                 "input_size": 1280}
        # data1 = {"exp_type": "organoid_AOPI","BF_input_path": r"H:\单图图库\细胞转染\100\1\A3_01_01_01.jpg",
        #                   "FL1_input_path": r"H:\单图图库\细胞转染\100\2\A3_01_01_01.jpg","FL2_input_path": r"H:\单图图库\细胞转染\100\4\A3_01_01_01.jpg",
        #       "BF_save_path":r"H:\test\A3_01_01_01_BF.jpg","FL1_save_path":r"H:\test\A3_01_01_01_FL1.jpg","FL2_save_path":r"H:\test\A3_01_01_01_FL2.jpg","merge_save_path":r"H:\test\A3_01_01_01_merge.jpg","data_save_path":r"H:\test\A3_01_01_01_Trans.csv","fcs_save_path":r"H:\test\A3_01_01_01_Trans.csv","cut_patch":4}
        # data1={"exp_type": "common_organoid_tracking", 'input_image_path': 'D:\\肝癌_追踪项目\\数据_类器官追踪\\himage_temp\\146-hGC-02_P4-day9\\images\\B10_01_BF_merge.jpg', 'conf_thres': 0.1, 'iou_thres': 0.1, 'max_shape': 1280, 'slice_pred_thed_max': 4128, 'gray_thred': 0, 'sharpne_thresh': 0, 'image_save_path': 'D:\\肝癌_追踪项目\\数据_类器官追踪\\himage_result\\B10_01_BF_merge_146-hGC-02_P4-day9.jpg', 'data_save_path': 'D:\\肝癌_追踪项目\\数据_类器官追踪\\himage_result\\B10_01_BF_merge146-hGC-02_P4-day9.csv', 'fcs_save_path': 'D:\\肝癌_追踪项目\\数据_类器官追踪\\himage_result\\B10_01_BF_merge_fcs146-hGC-02_P4-day9.csv', 'track_id': '', 'track_mode': None, 'track_image_save_path': 'D:\\肝癌_追踪项目\\数据_类器官追踪\\himage_result\\B10_01_BF_merge_track146-hGC-02_P4-day9.jpg', 'track_data_save_path': 'D:\\肝癌_追踪项目\\数据_类器官追踪\\himage_result\\B10_01_BF_merge_track146-hGC-02_P4-day9.csv', 'track_fcs_save_path': 'D:\\肝癌_追踪项目\\数据_类器官追踪\\himage_result\\B10_01_BF_merge_track_fcs146-hGC-02_P4-day9.csv'}
        # data1 = {"exp_type": "transfection","BF_input_path": r"H:\单图图库\细胞转染\100\1\A3_01_01_01.jpg",
        #                   "FL1_input_path": r"H:\单图图库\细胞转染\100\2\A3_01_01_01.jpg","FL2_input_path": r"H:\单图图库\细胞转染\100\4\A3_01_01_01.jpg",
        #       "BF_save_path":r"H:\test\A3_01_01_01_BF.jpg","FL1_save_path":r"H:\test\A3_01_01_01_FL1.jpg","FL2_save_path":r"H:\test\A3_01_01_01_FL2.jpg","merge_save_path":r"H:\test\A3_01_01_01_merge.jpg","data_save_path":r"H:\test\A3_01_01_01_Trans.csv","fcs_save_path":r"H:\test\A3_01_01_01_Trans.csv",
        #
        #          "BF_conf_thrd":0.1,"BF_iou_thrd":0.1,"FL1_conf_thrd":0.1,"FL1_iou_thrd":0.1,"FL2_conf_thrd":0.1,"FL2_iou_thrd":0.1,"FL3_conf_thrd":0.1,"FL3_iou_thrd":0.1,
        #          "FL4_conf_thrd":0.1,"FL4_iou_thrd":0.1}
        # data1 = {"exp_type": "suspension_BF", "input_image_path": "H:\\单图图库\\d3C3_01.jpg",
        #          "image_save_path": "H:\\test\\B2_01_res_B_origin_AIa.jpg",
        #          "data_save_path": "H:\\test\\B2_01_res_B_origin_AIa.csv", "conf_thrd": 0.5, "iou_thrd": 0.1,"fast_mode":1,
        #          "input_size": 1280}
        # data1 = {"exp_type": "transfection", "BF_input_path": r"H:\单图图库\细胞转染\100\1\A3_01_01_01.jpg",
        #          "FL1_input_path": r"H:\单图图库\细胞转染\100\2\A3_01_01_01.jpg",
        #          "FL2_input_path": r"H:\单图图库\细胞转染\100\4\A3_01_01_01.jpg",
        #          "BF_save_path": r"H:\test\A3_01_01_01_BF.jpg", "FL1_save_path": r"H:\test\A3_01_01_01_FL1.jpg",
        #          "FL2_save_path": r"H:\test\A3_01_01_01_FL2.jpg", "merge_save_path": r"H:\test\A3_01_01_01_merge.jpg",
        #          "data_save_path": r"H:\test\A3_01_01_01_Trans.csv", "fcs_save_path": r"H:\test\A3_01_01_01_Trans.csv",
        #          "cut_patch": 4}
        # data1 = {"exp_type": "organoidsFL", "BF_input_path": r"H:\单图图库\细胞转染\100\1\A3_01_01_01.jpg",
        #          "FL1_input_path": r"H:\单图图库\细胞转染\100\2\A3_01_01_01.jpg",
        #          "FL2_input_path": r"H:\单图图库\细胞转染\100\4\A3_01_01_01.jpg",
        #          "BF_save_path": r"H:\test\A3_01_01_01_BF.jpg", "FL1_save_path": r"H:\test\A3_01_01_01_FL1.jpg",
        #          "FL2_save_path": r"H:\test\A3_01_01_01_FL2.jpg", "merge_save_path": r"H:\test\A3_01_01_01_merge.jpg",
        #          "data_save_path": r"H:\test\A3_01_01_01_Trans.csv", "fcs_save_path": r"H:\test\A3_01_01_01_Trans.csv",
        #          "cut_patch": 4}
        # data1 = {"exp_type": "organoidsFL", "BF_input_path": r"H:\类器官AOPI数据用例-230109\Images\B2_01_source_BF.jpg",
        #          "BF_labelme_path":r"H:\类器官AOPI数据用例-230109\label\B2_01_BF.json",
        #          "FL1_input_path": r"H:\类器官AOPI数据用例-230109\Images\B2_01_source_FL1.jpg",
        #          "FL2_input_path": r"H:\类器官AOPI数据用例-230109\Images\B2_01_source_FL2.jpg",
        #          "BF_save_path": r"H:\test\A3_01_01_01_BF.jpg", "FL1_save_path": r"H:\test\A3_01_01_01_FL1.jpg",
        #          "FL2_save_path": r"H:\test\A3_01_01_01_FL2.jpg", "merge_save_path": r"H:\test\A3_01_01_01_merge.jpg",
        #          "data_save_path": r"H:\test\A3_01_01_01_Trans.csv", "fcs_save_path": r"H:\test\A3_01_01_01_Trans.csv"}
        #data1={"exp_type": "plaque", "input_image_path": "H:\\单图图库\\d3C3_01.jpg", "image_save_path": "H:\\test\\B2_01_res_B_origin_AIa.jpg", "data_save_path": "H:\\test\\B2_01_res_B_origin_AIa.csv", "conf_thrd": 0.5, "iou_thrd": 0.1, "input_size": 1280}
        res0=requests.post(get_task_url,data=json.dumps(data1).encode())
        print(res0.json())
        # res1 = requests.post(get_task_url,data=json.dumps(data2).encode())
        # print(res1.json())

if __name__ == '__main__':
    add_task(1)
    # gpus_number = 4
    # for i in range(gpus_number):
    #     multiprocessing.Process(name=f"子进程-{gpus_number}",target=main,args=(i,)).start()
