7767517
192 216
Input                    images                   0 1 images
YoloV5Focus              focus                    1 1 images 207
Convolution              Conv_41                  1 1 207 208 0=32 1=3 4=1 5=1 6=3456
Swish                    Mul_43                   1 1 208 210
Convolution              Conv_44                  1 1 210 211 0=64 1=3 3=2 4=1 5=1 6=18432
Swish                    Mul_46                   1 1 211 213
Split                    splitncnn_0              1 2 213 213_splitncnn_0 213_splitncnn_1
Convolution              Conv_47                  1 1 213_splitncnn_1 214 0=32 1=1 5=1 6=2048
Swish                    Mul_49                   1 1 214 216
Split                    splitncnn_1              1 2 216 216_splitncnn_0 216_splitncnn_1
Convolution              Conv_50                  1 1 216_splitncnn_1 217 0=32 1=1 5=1 6=1024
Swish                    Mul_52                   1 1 217 219
Convolution              Conv_53                  1 1 219 220 0=32 1=3 4=1 5=1 6=9216
Swish                    Mul_55                   1 1 220 222
BinaryOp                 Add_56                   2 1 216_splitncnn_0 222 223
Convolution              Conv_57                  1 1 223 224 0=32 1=1 6=1024
Convolution              Conv_58                  1 1 213_splitncnn_0 225 0=32 1=1 6=2048
Concat                   Concat_59                2 1 224 225 226
BatchNorm                BatchNormalization_60    1 1 226 227 0=64
ReLU                     LeakyRelu_61             1 1 227 228 0=1.000000e-01
Convolution              Conv_62                  1 1 228 229 0=64 1=1 5=1 6=4096
Swish                    Mul_64                   1 1 229 231
Convolution              Conv_65                  1 1 231 232 0=128 1=3 3=2 4=1 5=1 6=73728
Swish                    Mul_67                   1 1 232 234
Split                    splitncnn_2              1 2 234 234_splitncnn_0 234_splitncnn_1
Convolution              Conv_68                  1 1 234_splitncnn_1 235 0=64 1=1 5=1 6=8192
Swish                    Mul_70                   1 1 235 237
Split                    splitncnn_3              1 2 237 237_splitncnn_0 237_splitncnn_1
Convolution              Conv_71                  1 1 237_splitncnn_1 238 0=64 1=1 5=1 6=4096
Swish                    Mul_73                   1 1 238 240
Convolution              Conv_74                  1 1 240 241 0=64 1=3 4=1 5=1 6=36864
Swish                    Mul_76                   1 1 241 243
BinaryOp                 Add_77                   2 1 237_splitncnn_0 243 244
Split                    splitncnn_4              1 2 244 244_splitncnn_0 244_splitncnn_1
Convolution              Conv_78                  1 1 244_splitncnn_1 245 0=64 1=1 5=1 6=4096
Swish                    Mul_80                   1 1 245 247
Convolution              Conv_81                  1 1 247 248 0=64 1=3 4=1 5=1 6=36864
Swish                    Mul_83                   1 1 248 250
BinaryOp                 Add_84                   2 1 244_splitncnn_0 250 251
Split                    splitncnn_5              1 2 251 251_splitncnn_0 251_splitncnn_1
Convolution              Conv_85                  1 1 251_splitncnn_1 252 0=64 1=1 5=1 6=4096
Swish                    Mul_87                   1 1 252 254
Convolution              Conv_88                  1 1 254 255 0=64 1=3 4=1 5=1 6=36864
Swish                    Mul_90                   1 1 255 257
BinaryOp                 Add_91                   2 1 251_splitncnn_0 257 258
Convolution              Conv_92                  1 1 258 259 0=64 1=1 6=4096
Convolution              Conv_93                  1 1 234_splitncnn_0 260 0=64 1=1 6=8192
Concat                   Concat_94                2 1 259 260 261
BatchNorm                BatchNormalization_95    1 1 261 262 0=128
ReLU                     LeakyRelu_96             1 1 262 263 0=1.000000e-01
Convolution              Conv_97                  1 1 263 264 0=128 1=1 5=1 6=16384
Swish                    Mul_99                   1 1 264 266
Split                    splitncnn_6              1 2 266 266_splitncnn_0 266_splitncnn_1
Convolution              Conv_100                 1 1 266_splitncnn_1 267 0=256 1=3 3=2 4=1 5=1 6=294912
Swish                    Mul_102                  1 1 267 269
Split                    splitncnn_7              1 2 269 269_splitncnn_0 269_splitncnn_1
Convolution              Conv_103                 1 1 269_splitncnn_1 270 0=128 1=1 5=1 6=32768
Swish                    Mul_105                  1 1 270 272
Split                    splitncnn_8              1 2 272 272_splitncnn_0 272_splitncnn_1
Convolution              Conv_106                 1 1 272_splitncnn_1 273 0=128 1=1 5=1 6=16384
Swish                    Mul_108                  1 1 273 275
Convolution              Conv_109                 1 1 275 276 0=128 1=3 4=1 5=1 6=147456
Swish                    Mul_111                  1 1 276 278
BinaryOp                 Add_112                  2 1 272_splitncnn_0 278 279
Split                    splitncnn_9              1 2 279 279_splitncnn_0 279_splitncnn_1
Convolution              Conv_113                 1 1 279_splitncnn_1 280 0=128 1=1 5=1 6=16384
Swish                    Mul_115                  1 1 280 282
Convolution              Conv_116                 1 1 282 283 0=128 1=3 4=1 5=1 6=147456
Swish                    Mul_118                  1 1 283 285
BinaryOp                 Add_119                  2 1 279_splitncnn_0 285 286
Split                    splitncnn_10             1 2 286 286_splitncnn_0 286_splitncnn_1
Convolution              Conv_120                 1 1 286_splitncnn_1 287 0=128 1=1 5=1 6=16384
Swish                    Mul_122                  1 1 287 289
Convolution              Conv_123                 1 1 289 290 0=128 1=3 4=1 5=1 6=147456
Swish                    Mul_125                  1 1 290 292
BinaryOp                 Add_126                  2 1 286_splitncnn_0 292 293
Convolution              Conv_127                 1 1 293 294 0=128 1=1 6=16384
Convolution              Conv_128                 1 1 269_splitncnn_0 295 0=128 1=1 6=32768
Concat                   Concat_129               2 1 294 295 296
BatchNorm                BatchNormalization_130   1 1 296 297 0=256
ReLU                     LeakyRelu_131            1 1 297 298 0=1.000000e-01
Convolution              Conv_132                 1 1 298 299 0=256 1=1 5=1 6=65536
Swish                    Mul_134                  1 1 299 301
Split                    splitncnn_11             1 2 301 301_splitncnn_0 301_splitncnn_1
Convolution              Conv_135                 1 1 301_splitncnn_1 302 0=512 1=3 3=2 4=1 5=1 6=1179648
Swish                    Mul_137                  1 1 302 304
Convolution              Conv_138                 1 1 304 305 0=256 1=1 5=1 6=131072
Swish                    Mul_140                  1 1 305 307
Split                    splitncnn_12             1 4 307 307_splitncnn_0 307_splitncnn_1 307_splitncnn_2 307_splitncnn_3
Pooling                  MaxPool_141              1 1 307_splitncnn_3 308 1=5 3=2 5=1
Pooling                  MaxPool_142              1 1 307_splitncnn_2 309 1=9 3=4 5=1
Pooling                  MaxPool_143              1 1 307_splitncnn_1 310 1=13 3=6 5=1
Concat                   Concat_144               4 1 307_splitncnn_0 308 309 310 311
Convolution              Conv_145                 1 1 311 312 0=512 1=1 5=1 6=524288
Swish                    Mul_147                  1 1 312 314
Split                    splitncnn_13             1 2 314 314_splitncnn_0 314_splitncnn_1
Convolution              Conv_148                 1 1 314_splitncnn_1 315 0=256 1=1 5=1 6=131072
Swish                    Mul_150                  1 1 315 317
Convolution              Conv_151                 1 1 317 318 0=256 1=1 5=1 6=65536
Swish                    Mul_153                  1 1 318 320
Convolution              Conv_154                 1 1 320 321 0=256 1=3 4=1 5=1 6=589824
Swish                    Mul_156                  1 1 321 323
Convolution              Conv_157                 1 1 323 324 0=256 1=1 6=65536
Convolution              Conv_158                 1 1 314_splitncnn_0 325 0=256 1=1 6=131072
Concat                   Concat_159               2 1 324 325 326
BatchNorm                BatchNormalization_160   1 1 326 327 0=512
ReLU                     LeakyRelu_161            1 1 327 328 0=1.000000e-01
Convolution              Conv_162                 1 1 328 329 0=512 1=1 5=1 6=262144
Swish                    Mul_164                  1 1 329 331
Convolution              Conv_165                 1 1 331 332 0=256 1=1 5=1 6=131072
Swish                    Mul_167                  1 1 332 334
Split                    splitncnn_14             1 2 334 334_splitncnn_0 334_splitncnn_1
Interp                   Resize_169               1 1 334_splitncnn_1 339 0=1 1=2.000000e+00 2=2.000000e+00
Concat                   Concat_170               2 1 339 301_splitncnn_0 340
Split                    splitncnn_15             1 2 340 340_splitncnn_0 340_splitncnn_1
Convolution              Conv_171                 1 1 340_splitncnn_1 341 0=128 1=1 5=1 6=65536
Swish                    Mul_173                  1 1 341 343
Convolution              Conv_174                 1 1 343 344 0=128 1=1 5=1 6=16384
Swish                    Mul_176                  1 1 344 346
Convolution              Conv_177                 1 1 346 347 0=128 1=3 4=1 5=1 6=147456
Swish                    Mul_179                  1 1 347 349
Convolution              Conv_180                 1 1 349 350 0=128 1=1 6=16384
Convolution              Conv_181                 1 1 340_splitncnn_0 351 0=128 1=1 6=65536
Concat                   Concat_182               2 1 350 351 352
BatchNorm                BatchNormalization_183   1 1 352 353 0=256
ReLU                     LeakyRelu_184            1 1 353 354 0=1.000000e-01
Convolution              Conv_185                 1 1 354 355 0=256 1=1 5=1 6=65536
Swish                    Mul_187                  1 1 355 357
Convolution              Conv_188                 1 1 357 358 0=128 1=1 5=1 6=32768
Swish                    Mul_190                  1 1 358 360
Split                    splitncnn_16             1 2 360 360_splitncnn_0 360_splitncnn_1
Interp                   Resize_192               1 1 360_splitncnn_1 365 0=1 1=2.000000e+00 2=2.000000e+00
Concat                   Concat_193               2 1 365 266_splitncnn_0 366
Split                    splitncnn_17             1 2 366 366_splitncnn_0 366_splitncnn_1
Convolution              Conv_194                 1 1 366_splitncnn_1 367 0=64 1=1 5=1 6=16384
Swish                    Mul_196                  1 1 367 369
Convolution              Conv_197                 1 1 369 370 0=64 1=1 5=1 6=4096
Swish                    Mul_199                  1 1 370 372
Convolution              Conv_200                 1 1 372 373 0=64 1=3 4=1 5=1 6=36864
Swish                    Mul_202                  1 1 373 375
Convolution              Conv_203                 1 1 375 376 0=64 1=1 6=4096
Convolution              Conv_204                 1 1 366_splitncnn_0 377 0=64 1=1 6=16384
Concat                   Concat_205               2 1 376 377 378
BatchNorm                BatchNormalization_206   1 1 378 379 0=128
ReLU                     LeakyRelu_207            1 1 379 380 0=1.000000e-01
Convolution              Conv_208                 1 1 380 381 0=128 1=1 5=1 6=16384
Swish                    Mul_210                  1 1 381 383
Split                    splitncnn_18             1 2 383 383_splitncnn_0 383_splitncnn_1
Convolution              Conv_211                 1 1 383_splitncnn_1 384 0=128 1=3 3=2 4=1 5=1 6=147456
Swish                    Mul_213                  1 1 384 386
Concat                   Concat_214               2 1 386 360_splitncnn_0 387
Split                    splitncnn_19             1 2 387 387_splitncnn_0 387_splitncnn_1
Convolution              Conv_215                 1 1 387_splitncnn_1 388 0=128 1=1 5=1 6=32768
Swish                    Mul_217                  1 1 388 390
Convolution              Conv_218                 1 1 390 391 0=128 1=1 5=1 6=16384
Swish                    Mul_220                  1 1 391 393
Convolution              Conv_221                 1 1 393 394 0=128 1=3 4=1 5=1 6=147456
Swish                    Mul_223                  1 1 394 396
Convolution              Conv_224                 1 1 396 397 0=128 1=1 6=16384
Convolution              Conv_225                 1 1 387_splitncnn_0 398 0=128 1=1 6=32768
Concat                   Concat_226               2 1 397 398 399
BatchNorm                BatchNormalization_227   1 1 399 400 0=256
ReLU                     LeakyRelu_228            1 1 400 401 0=1.000000e-01
Convolution              Conv_229                 1 1 401 402 0=256 1=1 5=1 6=65536
Swish                    Mul_231                  1 1 402 404
Split                    splitncnn_20             1 2 404 404_splitncnn_0 404_splitncnn_1
Convolution              Conv_232                 1 1 404_splitncnn_1 405 0=256 1=3 3=2 4=1 5=1 6=589824
Swish                    Mul_234                  1 1 405 407
Concat                   Concat_235               2 1 407 334_splitncnn_0 408
Split                    splitncnn_21             1 2 408 408_splitncnn_0 408_splitncnn_1
Convolution              Conv_236                 1 1 408_splitncnn_1 409 0=256 1=1 5=1 6=131072
Swish                    Mul_238                  1 1 409 411
Convolution              Conv_239                 1 1 411 412 0=256 1=1 5=1 6=65536
Swish                    Mul_241                  1 1 412 414
Convolution              Conv_242                 1 1 414 415 0=256 1=3 4=1 5=1 6=589824
Swish                    Mul_244                  1 1 415 417
Convolution              Conv_245                 1 1 417 418 0=256 1=1 6=65536
Convolution              Conv_246                 1 1 408_splitncnn_0 419 0=256 1=1 6=131072
Concat                   Concat_247               2 1 418 419 420
BatchNorm                BatchNormalization_248   1 1 420 421 0=512
ReLU                     LeakyRelu_249            1 1 421 422 0=1.000000e-01
Convolution              Conv_250                 1 1 422 423 0=512 1=1 5=1 6=262144
Swish                    Mul_252                  1 1 423 425
Convolution              Conv_253                 1 1 383_splitncnn_0 426 0=21 1=1 5=1 6=2688
Reshape                  Reshape_267              1 1 426 444 0=-1 1=7 2=3
Permute                  Transpose_268            1 1 444 output 0=1
Convolution              Conv_269                 1 1 404_splitncnn_0 446 0=21 1=1 5=1 6=5376
Reshape                  Reshape_283              1 1 446 464 0=-1 1=7 2=3
Permute                  Transpose_284            1 1 464 465 0=1
Convolution              Conv_285                 1 1 425 466 0=21 1=1 5=1 6=10752
Reshape                  Reshape_299              1 1 466 484 0=-1 1=7 2=3
Permute                  Transpose_300            1 1 484 485 0=1
