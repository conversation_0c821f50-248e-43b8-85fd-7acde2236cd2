import json

import tifffile
from imutils.paths import list_images
import math
import os
os.environ["OPENCV_IO_MAX_IMAGE_PIXELS"] = str(pow(2, 50))


from tqdm import tqdm
from sahi.slicing import slice_image
import time
from lsnms import nms as naive_nms
from PIL import ImageFile
from PIL import Image
import numpy as np

import cv2

from utils.crop_main_body import crop_muti_image
from utils.GPU_Predictor_dynamic import YoloSegTRT, convert_from_image_to_cv2, convert_from_cv2_to_image

from loguru import logger as log
import traceback
ImageFile.LOAD_TRUNCATED_IMAGES = True
Image.MAX_IMAGE_PIXELS = None


class Yolov5Seg(YoloSegTRT):
    def __init__(self, model_file_path):
        super().__init__(model_file_path)
        self.jyh_img = None
        self.jyh_img_crop = None

    def slice_detect(self, input_img,
                     conf_thres=0.1,
                     iou_thres=0.1,
                     max_shape=1280,
                     slice_pred_thed_max=4128,
                     gray_thred=0,
                     sharpne_thresh=0,
                     output_img_path=None,
                     data_save_path=None,
                     fcs_save_path=None,
                     mask_save_path=None,
                     input_labelme_path=""
                     ):

        self.max_shape = max_shape
        img_origin = None
        img_origin_shape = None
        log.info(f"conf_thres {conf_thres} iou_thres {iou_thres} max_shape {self.max_shape}")
        if isinstance(input_img, str):
            self.jyh_img = cv2.imdecode(np.fromfile(input_img, dtype=np.uint8), cv2.IMREAD_COLOR)
            try:
                if "_cut.jpg" in input_img:
                    img_origin = cv2.imdecode(np.fromfile(input_img[:-8] + "_merge_src.jpg", dtype=np.uint8),
                                              cv2.IMREAD_COLOR)
                else:
                    img_origin = cv2.imdecode(np.fromfile(input_img[:-4] + "_src.jpg", dtype=np.uint8),
                                              cv2.IMREAD_COLOR)
            except Exception as e:
                img_origin = self.jyh_img.copy()
                log.info("未找到原图,使用均一化图计算")
            img_origin_shape = img_origin.shape[:2]
        elif isinstance(input_img, np.ndarray):
            img_origin = input_img
            img_origin_shape = img_origin.shape[:2]
        else:
            print("error img")
        # 抠图
        crop_bboxs = crop_muti_image(img_origin)
        print("img_origin_shape", img_origin_shape, "crop_bboxs", crop_bboxs)
        end_analysis_result = ','.join(['胶滴号', '细胞编号', '属性值', '通道', 'X坐标', 'Y坐标', '团属性', '面积',
                                      '周长', '长轴', '短轴', '圆度', '边缘锐利度', '边缘差异率', '平均灰度',
                                      '累计灰度',
                                      '最大灰度', '最小灰度', '灰度差', '背景差异', '正圆率', '亮度', '轮廓']) + "\n"
        time_start = time.time()
        durations_in_seconds = dict()
        cell_number = 0
        if mask_save_path is None or mask_save_path=="":
            mask_save_path = output_img_path[:-4]
        obj_mask = []
        if os.path.exists(input_labelme_path):
            end_det = []
            end_segments = []
            try:
                log.info(f"use {input_labelme_path} result computer info")
                with open(input_labelme_path, "r") as f:
                    labelme_json = json.load(f)
                shapes = labelme_json["shapes"]
                end_det = np.empty([len(shapes), 6])
                for i in range(len(shapes)):
                    score = 1.0
                    if labelme_json["shapes"][i]["shape_type"] == "polygon":
                        points = labelme_json["shapes"][i]["points"]
                        end_segments.append(np.array(points).astype('float32'))
                        xs = [p[0] for p in points]
                        ys = [p[1] for p in points]
                        minx = min(xs)
                        maxx = max(xs)
                        miny = min(ys)
                        maxy = max(ys)
                        # 补充下面的代码实现添加shape的外部矩形到cell_info
                        class_id = 0
                        end_det[i] = np.array([minx, miny, maxx, maxy, score, class_id])
            except Exception as e:
                error_msg = traceback.format_exc()
                log.error(f"labelme_json error {error_msg}")
            for jiaodi_number, crop_bbox in enumerate(crop_bboxs, start=1):
                img0 = img_origin[crop_bbox[1]:crop_bbox[3], crop_bbox[0]:crop_bbox[2]]
                self.jyh_img_crop = self.jyh_img[crop_bbox[1]:crop_bbox[3], crop_bbox[0]:crop_bbox[2]]
                infotime = time.time()
                img_result, analysis_result, cell_number, = self.get_info(jiaodi_number, cell_number, img0, end_det,
                                                                        end_segments, crop_bbox,
                                                                        obj_mask, gray_thred,
                                                                        sharpne_thresh)
                durations_in_seconds["get_info"] = time.time() - infotime
                print("slice_durations_in_seconds", durations_in_seconds)
                self.jyh_img[crop_bbox[1]:crop_bbox[3], crop_bbox[0]:crop_bbox[2]] = img_result  # 改变原图的抠图部分
                end_analysis_result += analysis_result
        else:
            for jiaodi_number, crop_bbox in enumerate(crop_bboxs, start=1):
                img0 = img_origin[crop_bbox[1]:crop_bbox[3], crop_bbox[0]:crop_bbox[2]]
                self.jyh_img_crop = self.jyh_img[crop_bbox[1]:crop_bbox[3], crop_bbox[0]:crop_bbox[2]]
                print("img_crop_shape", img0.shape, "jiaodi_number", jiaodi_number, " crop_bbox ", crop_bbox)
                # cv2.imencode('.jpg', img0)[1].tofile(output_img_path[:-4] + "_" + str(jiaodi_number) + ".jpg")
                perform_full_pred = True if (max(img0.shape) < slice_pred_thed_max) else False  # whole pic det
                log.info(f"perform_full: {perform_full_pred}")
                slice_time_start = time.time()
                # perform sliced prediction
                object_prediction_list = []
                slice_start = time.time()
                if perform_full_pred:
                    # perform full prediction
                    log.info('----------------------------------只整图识别---------------------------------')
                    for i_shap in list(set(list([640, 960, 1280, self.max_shape]))):
                        self.max_shape = i_shap
                        prediction_result = self.get_prediction(
                            image=img0,
                            conf_threshold=conf_thres,
                            iou_threshold=iou_thres,
                            shift_amount=None,
                        )
                        object_prediction_list.extend(prediction_result)
                else:
                    print('-------------------------------------整图预测并蒙版-------------------------------------')
                    for i_shap in [640, 1280]:
                        self.max_shape = i_shap
                        print("self.max_shape:", self.max_shape)
                        prediction_result = self.get_prediction(
                            image=img0,
                            conf_threshold=conf_thres,
                            iou_threshold=iou_thres,
                            shift_amount=None
                        )
                        object_prediction_list.extend(prediction_result)
                    masked_img0 = self.mask_crop_processing(img0, object_prediction_list, conf_thres, iou_thres)
                    log.info('---------------------------------再切片预测--------------------------------')
                    num_batch = 1
                    # self.max_shape = 4096
                    slice_image_result = slice_image(
                        image=convert_from_cv2_to_image(masked_img0),
                        slice_height=max_shape,
                        slice_width=max_shape,
                        overlap_height_ratio=0.5,
                        overlap_width_ratio=0.5
                    )
                    num_slices = len(slice_image_result)
                    print("Number of slices:", num_slices)
                    durations_in_seconds["slice"] = slice_start - slice_time_start

                    # create prediction input,每张slice图片做预测
                    num_group = int(num_slices / num_batch)
                    for group_ind in range(num_group):
                        image_list = []
                        shift_amount_list = []
                        for image_ind in range(num_batch):
                            image_list.append(slice_image_result.images[group_ind * num_batch + image_ind])
                            shift_amount_list.append(slice_image_result.starting_pixels[group_ind * num_batch + image_ind])
                            print(f"总{num_slices}片处理第{group_ind + 1}片--", image_list[0].shape, shift_amount_list[0])
                            for i_shape in [640, 960, 1280]:
                                self.max_shape = i_shape
                                prediction_result = self.get_prediction(
                                    image=convert_from_image_to_cv2(image_list[0]),
                                    conf_threshold=conf_thres,
                                    iou_threshold=iou_thres,
                                    shift_amount=shift_amount_list[0],
                                )
                                object_prediction_list.extend(prediction_result)
                detection_start = time.time()
                durations_in_seconds["detection"] = detection_start - slice_start
                # remove empty predictions
                object_prediction_list = [object_prediction for object_prediction in object_prediction_list if
                                          object_prediction]
                end_det = []
                end_segments = []
                if len(object_prediction_list) > 0:
                    # print(len(object_prediction_list),[x[0].shape for x in object_prediction_list])
                    det = np.concatenate([x[0] for x in object_prediction_list], axis=0)
                    old_det = np.concatenate([x[2] for x in object_prediction_list], axis=0)
                    try:
                        keep = naive_nms(det[:, :4], det[:, 4], iou_threshold=iou_thres, score_threshold=conf_thres)
                        end_det = det[keep]
                        # print('keep:', keep)  # [...]
                        print("jiaodi_number", jiaodi_number, " cell_number", end_det.shape[0])
                        nmsstart = time.time()
                        durations_in_seconds["NMS"] = nmsstart - detection_start
                        proto_info = []
                        [proto_info.extend(x[1]) for x in object_prediction_list]
                        for keep_number in tqdm(keep):
                            # print(f'+++++++++++++++++++++++++++/{list(keep).index(keep_number)}/+++++++++++++++++++++++++++')
                            probo = proto_info[keep_number][0]
                            input_data_shape = proto_info[keep_number][1]  # [np, (1280, 1280), (7146, 6776, 3), [0, 0]]
                            masks = self.process_mask(
                                probo,
                                np.expand_dims(old_det[keep_number, 6:], axis=0),
                                np.expand_dims(old_det[keep_number, :4], axis=0),
                                input_data_shape,
                                upsample=True)  # CHW,[instances_num, 640, 640]
                            segments = self.masks2segments(masks)  # mask to findContours points [[]]
                            del masks
                            # findContours points do padding to ori img
                            segments = [self.scale_segments(proto_info[keep_number][1], x, proto_info[keep_number][2],
                                                            proto_info[keep_number][3]) for num, x in
                                        enumerate(segments)]  # list
                            end_segments.extend(segments)
                            maskend = time.time()
                            durations_in_seconds["mask"] = maskend - nmsstart
                    except Exception as e:
                        log.error(e)
                infotime = time.time()
                # 绘图
                try:
                    self.send_response("MsgTime", self.msg_id, {"tid": self.task_id, "Time": int(len(end_segments) * 1) + 600})
                except Exception as e:
                    log.error(f"send MsgTime error:{e}")
                img_result, analysis_result, cell_number, = self.get_info(jiaodi_number, cell_number, img0, end_det,
                                                                        end_segments, crop_bbox,obj_mask, gray_thred,
                                                                        sharpne_thresh)
                durations_in_seconds["get_info"] = time.time() - infotime
                print("slice_durations_in_seconds", durations_in_seconds)
                self.jyh_img[crop_bbox[1]:crop_bbox[3], crop_bbox[0]:crop_bbox[2]] = img_result  # 改变原图的抠图部分
                end_analysis_result += analysis_result

        try:
            if os.path.exists(data_save_path):
                os.remove(data_save_path)
            with open(data_save_path, 'a+', encoding='utf-8') as f:
                f.write(end_analysis_result)
        except Exception as e:
            log.error(f"data_save_path {data_save_path} not exists", exc_info=True)
        try:
            if fcs_save_path != '':
                if os.path.exists(fcs_save_path):
                    os.remove(fcs_save_path)
                with open(fcs_save_path, 'a+', encoding='utf-8') as f:
                    f.write(end_analysis_result)
        except Exception as e:
            log.error(f"fcs_save_path {fcs_save_path} not exists", exc_info=True)
        try:
            cv2.imencode('.jpg', self.jyh_img)[1].tofile(output_img_path)
        except Exception as e:
            print("保存失败")
        durations_in_seconds["cost time"] = time.time() - time_start
        self.save_tiff_mask(mask_save_path,obj_mask,img_shape=img_origin_shape)
        log.info(durations_in_seconds)
        return 0

    def save_tiff_mask(self, mask_save_path, all_cell_masks, img_shape=None, channel="BF", compression_level=5,
                       bigtiff=False):
        """保存细胞掩码为TIFF文件，支持批处理和进度显示"""

        try:
            start_time = time.time()
            # 确保目录存在
            os.makedirs(os.path.dirname(mask_save_path), exist_ok=True)
            base_path, ext = os.path.splitext(mask_save_path)
            if ext.lower() in [".jpg", ".png", ".bmp", ".jpeg"]:
                mask_save_path = base_path
            if not mask_save_path.lower().endswith(".tif"):
                mask_save_path += "_multimasks.tif"
            total_cells = len(all_cell_masks)
            if total_cells == 0:
                log.warning(f"没有找到有效细胞掩码，跳过保存TIFF掩码: {mask_save_path}")
                return False
            log.info(f"开始保存 {total_cells} 个细胞掩码到 {mask_save_path}")
            # 创建TiffWriter对象，使用适当的参数
            if total_cells > 255:
                log.info("启用单页tiff保存")
                if len(img_shape) == 3:
                    img_shape = img_shape[:2]
                mask = np.zeros(img_shape, dtype=np.uint16)
                for cell_mask_info in all_cell_masks:
                    try:
                        tx, ty, bx, by = cell_mask_info["boxs"]
                        cell_index = cell_mask_info["cell_number"]
                        cell_mask = cell_mask_info["mask"].astype(np.uint16)
                        cell_mask *= cell_index
                        non_zero_indices = np.nonzero(cell_mask)
                        new_y_indices = non_zero_indices[0] + ty
                        new_x_indices = non_zero_indices[1] + tx
                        mask[new_y_indices, new_x_indices] = cell_mask[non_zero_indices]
                    except Exception as e:
                        print(e)
                        continue
                with tifffile.TiffWriter(mask_save_path, bigtiff=bigtiff) as obj_mask:
                    obj_mask.write(
                        mask,
                        metadata={'total_cells': total_cells, 'shape': img_shape, 'channels': channel},
                        compression="zlib",  # 使用zlib压缩,平衡速度和压缩率
                        compressionargs={'level': compression_level},  # 使用压缩级别5以提高速度
                        contiguous=True
                    )
            else:
                log.info("启用多页tiff保存")
                # 使用上下文管理器创建TiffWriter对象
                with tifffile.TiffWriter(mask_save_path, bigtiff=bigtiff) as obj_mask:
                    # 批量处理
                    for cell_mask_info in all_cell_masks:
                        obj_mask.write(
                            cell_mask_info["mask"],
                            metadata={'cell_number': cell_mask_info["cell_number"], 'boxs': cell_mask_info["boxs"],
                                      'channels': channel},
                            compression="zlib",  
                            compressionargs={'level': compression_level},  # 使用压缩级别5以提高速度
                        )
            # 计算并显示性能统计
            elapsed_time = time.time() - start_time
            file_size_mb = os.path.getsize(mask_save_path) / (1024 * 1024)
            log.info(f"TIFF掩码保存完成: {mask_save_path}")
            log.info(
                f"总耗时: {elapsed_time:.2f}秒, 文件大小: {file_size_mb:.2f}MB, 速度: {file_size_mb / elapsed_time:.2f}MB/s")
            return True
        except Exception as e:
            log.error(f"保存TIFF掩码失败: {e}")
            log.error(traceback.format_exc())
            return False
    def get_info(self, jiaodi_number, cell_number, img, det, segments, crop_bbox, obj_mask, gray_thred=0,
                 sharpne_thresh=0):
        analysis_result = ""
        color_mask = np.zeros(img.shape, dtype=np.uint8)
        img_copy_gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        laplacian_img = cv2.Laplacian(img_copy_gray, cv2.CV_64F)
        fix_size = 1280
        line_thrkness = -1
        if max(img.shape) > fix_size:
            scale = max(img.shape) / fix_size
            resized_img = cv2.resize(img, None, fx=1 / scale, fy=1 / scale)  # 缩小
            laplacian_img = cv2.resize(laplacian_img, None, fx=1 / scale, fy=1 / scale)
        else:
            resized_img = img
            scale = 1
        del img_copy_gray

        if len(det):
            boxes, scores, class_ids = det[:, :4], det[:, 4], det[:, 5]
            # color = Colors()
            # custom_color = color(random.randint(0, 255))
            custom_color = (0, 100, 0)
            for cell_num, (box, score, class_id) in enumerate(zip(boxes, scores, class_ids)):
                # start_time=time.time()
                # custom_color = color(random.randint(0, 255))
                x1, y1, x2, y2 = box.astype(int)
                # 缩小的bbox
                scale_x1, scale_y1, scale_x2, scale_y2 = list(map(int, [i / scale for i in [x1, y1, x2, y2]]))
                if scale_x2 - scale_x1 == 0 or scale_y2 - scale_y1 == 0:
                    continue
                ctype = 1
                local_channel = 0
                center_x = int((x1 + x2) / 2)
                center_y = int((y1 + y2) / 2)
                # radius = int(((x2 - x1) + (y2 - y1)) / 4)
                group_type = ""  # 团属性
                if len(np.unique(segments[cell_num])) < 3:
                    continue
                try:
                    rect=cv2.minAreaRect(segments[cell_num])
                    # rect 的格式是 (center (x,y), (width, height), angle of rotation)
                    _, (minArea_width, minArea_height), _ = rect
                    major_axis_length=max(minArea_height,minArea_width)
                    minor_axis_length=min(minArea_height,minArea_width)
                except:
                    major_axis_length = max((x2 - x1), (y2 - y1))
                    minor_axis_length = min((x2 - x1), (y2 - y1))

                # 绘图
                # score_ = '%.3f' % score
                # cv2.rectangle(img, (x1, y1), (x2, y2), (0, 0, 255), thickness=1)
                # cv2.putText(img,
                #             # str(cell_num + 1), (x1, y1 - 10),
                #             str(f'{score_}_{cell_num + 1}'), (x1, y1 - 10),
                #             cv2.FONT_HERSHEY_SIMPLEX,
                #             1, (255, 0, 0),
                #             thickness=1)

                bbox_x, bbox_y, bbox_w, bbox_h = cv2.boundingRect(segments[cell_num])
                area = int(cv2.contourArea(segments[cell_num] / scale))  # scale放大倍数
                mask = np.zeros(resized_img.shape[:2], dtype=np.uint8)
                mask = cv2.drawContours(mask, np.array([segments[cell_num] / scale]).astype(int), -1, 1, -1)  # 绘图
                circle_mask = self.find_cricel_mask(mask, segments[cell_num])
                mask_crop = cv2.bitwise_and(resized_img, resized_img, mask=mask.astype(np.uint8))
                if max(mask_crop.shape) <= 640:
                    mask_scale = 1
                else:
                    mask_scale = max(mask_crop.shape) / 640
                mask_crop_reize = cv2.resize(mask_crop, (640, 640))
                edge_diff = cv2.Laplacian(mask_crop_reize, cv2.CV_64F).var() * mask_scale  # 整体锐利度

                # x, y, w, h = cv2.boundingRect(segments[cell_num]/scale)
                crop_img = mask_crop[scale_y1:scale_y2, scale_x1:scale_x2]  # resize的bbox
                crop_mask_gray = cv2.cvtColor(crop_img, cv2.COLOR_BGR2GRAY)
                ret, thresh = cv2.threshold(crop_mask_gray, gray_thred, 255, cv2.THRESH_BINARY)
                pxilecount = cv2.countNonZero(thresh)
                mean, std = cv2.meanStdDev(laplacian_img * circle_mask, mask=circle_mask)
                sharpne = std[0][0]  # 边缘锐利度 轮廓模糊度
                if sharpne <= sharpne_thresh:
                    continue
                avg_gray = np.mean(crop_mask_gray)  # 平均灰度
                sum_gray = np.sum(crop_mask_gray)  # 累计灰度
                min_gray = int(np.min(crop_mask_gray))  # 最小灰度
                max_gray = int(np.max(crop_mask_gray))  # 最大灰度
                diff_gray = pxilecount / area if area != 0 else 0  # 灰度差
                background_diff = ""
                luminance = np.mean(cv2.cvtColor(crop_img, cv2.COLOR_BGR2HSV)[:, :, 2])  # 亮度
                perimeter = cv2.arcLength(segments[cell_num], True)  # 周长
                origin_area = int(cv2.contourArea(segments[cell_num]))  # 原始面积
                roundness = (4 * math.pi * origin_area) / perimeter ** 2 if perimeter != 0 else 0  # 圆度
                roundness = 1 if roundness > 1 else roundness
                try:
                    centre, axes, angle = cv2.fitEllipse(segments[cell_num])  # 椭圆拟合
                    MAJ = np.argmax(axes)  # this is MAJor axis, 1 or 0
                    MIN = 1 - MAJ  # 0 or 1, minor axis
                    Eccentricity = np.sqrt(1 - (axes[MIN] / axes[MAJ]) ** 2)
                    circle_rate = Eccentricity  # 正圆率
                except:
                    circle_rate = np.sqrt(1 - (minor_axis_length / major_axis_length) ** 2) if major_axis_length != 0 else 0 # 正圆率
                del circle_mask, mask_crop, mask, crop_mask_gray, crop_img
                # 绘图
                try:
                    # img = cv2.drawContours(img,np.array([segments[cell_num]]).astype(int), -1,custom_color, 2)
                    color_mask = cv2.drawContours(color_mask, np.array([segments[cell_num]]).astype(int), -1,
                                                  custom_color, line_thrkness)  # all zeros np draw cont 浅绿
                    # 生成mask
                    save_mask = np.zeros(img.shape[:2], dtype=np.uint8)
                    save_mask = cv2.drawContours(save_mask, np.array([segments[cell_num]]).astype(int), -1, 1, -1)[
                                bbox_y:bbox_y + bbox_h, bbox_x:bbox_x + bbox_w]  # all zeros np draw cont 浅绿
                    obj_mask.append({'cell_number': cell_number + 1,"mask":save_mask.copy(),"boxs":[bbox_x+ crop_bbox[0],bbox_y+ crop_bbox[1],bbox_x + bbox_w+ crop_bbox[0],bbox_y + bbox_h+ crop_bbox[1]]})
                    del save_mask
                except Exception as e:
                    log.error("drawContours error", exc_info=True)

                res = [ctype, local_channel, center_x + crop_bbox[0], center_y + crop_bbox[1], group_type,
                       int(origin_area), int(perimeter),
                       int(major_axis_length), int(minor_axis_length), roundness, sharpne, edge_diff,
                       avg_gray, int(sum_gray * scale * scale), max_gray, min_gray, diff_gray, background_diff, circle_rate,
                       luminance,
                       f'{bbox_x+ crop_bbox[0]}:{bbox_y+ crop_bbox[1]}|{bbox_x+bbox_w+ crop_bbox[0]}:{bbox_y+bbox_h+ crop_bbox[1]}']
                cell_number = cell_number + 1
                analysis_result += str(jiaodi_number) + "," + str(cell_number) + "," + ",".join(map(str, res)) + "\n"
            log.info(f"get_info_cell_number {cell_number}")
        # start_time = time.time()
        color_mask = (color_mask * 0.7).astype(np.uint8)

        # img = cv2.add(img, color_mask)  # 合并zeros np 带轮廓（mask or ellipse）
        img = cv2.add(self.jyh_img_crop, color_mask)
        del color_mask, laplacian_img, resized_img
        # print("cost_time2", time.time() - start_time)
        return img, analysis_result, cell_number


if __name__ == "__main__":
    # v5_seg_detector = Yolov5Seg("maskrcnn_model/xiaochang_huizong117_2048_seg.engine")
    v5_seg_detector = Yolov5Seg(r"growh_APP/fei-people_230113_1060s/best_dynamic.engine")
    # ####单张图测试
    # ####单张图测试
    in_path = r"D:\B项目数据1103\Castor 小肠类器官4x 标图集 20221103\xiaochang_huizong"
    out_path = r'H:\test'
    for img_path in tqdm(list_images(in_path)):
        print(img_path)
        v5_seg_detector.slice_detect(input_img=img_path,
                                     conf_thres=0.1,
                                     iou_thres=0.1,
                                     max_shape=1280,
                                     slice_pred_thed_max=4128,
                                     gray_thred=0,
                                     sharpne_thresh=0,
                                     output_img_path=os.path.join(out_path, img_path.split(os.sep)[-1]),
                                     data_save_path=os.path.join(out_path, img_path.split(os.sep)[-1][:-4] + ".csv"),
                                     fcs_save_path=os.path.join(out_path, img_path.split(os.sep)[-1][:-4] + ".csv"))
        break
    # break

    # ####多图测试
    # image_dir = '/home/<USER>/PycharmProjects/xiaochang/1208'
    # files = os.listdir(image_dir)
    # for file in tqdm(files):
    #     in_path = f'{image_dir}/{file}'
    #     print(f'--------------------{in_path}--------------------------')
    #     out_path = '/home/<USER>/PycharmProjects/xiaochang/1208_result3/7'
    #     os.makedirs(out_path, exist_ok=True)
    #     v5_seg_detector.slice_detect(in_path, output_img_path=out_path, perform_full_pred=True,
    #                                  slice_pred_thed_max=4000)
