import json
import threading
import time
from pathlib import Path
import os

os.environ["OPENCV_IO_MAX_IMAGE_PIXELS"] = str(pow(2, 50))
import tifffile
from PIL import ImageDraw
from PIL import Image
from imutils.paths import list_images
from skimage import measure
from skimage.morphology import remove_small_objects
import numpy as np
import pycuda.driver as cuda #cuda和tensorrt放在cupy之前,否则运行会出错
import sys
# --- 添加 TensorRT 包检测和安装逻辑 ---
try:
    import tensorrt as trt
    # 打印 TensorRT 版本以确认导入成功
    print(f"TensorRT already installed. Version: {trt.__version__}")
    if int(trt.__version__.split(".")[0])!=8:
        print("tensorrt version is 10.x.x,pls install tensorrt 8.x.x")
        raise ImportError("tensorrt version is 10.x.x,pls install tensorrt 8.x.x")
except ImportError:
    print("TensorRT not found. Attempting to install from specified .whl file...")
    # 定义 .whl 文件的路径
    # 注意：这里的路径需要根据实际情况调整，例如可以从环境变量或配置文件中获取
    tensorrt_whl_path = r'C:\TensorRT-*******\python\tensorrt-*******-cp310-none-win_amd64.whl'
    # 检查 .whl 文件是否存在
    if not Path(tensorrt_whl_path).exists():
        print(f"Error: TensorRT .whl file not found at {tensorrt_whl_path}. Please check the path.")
        # 如果 .whl 文件不存在，我们无法自动安装，程序可能需要退出或采取其他措施
        sys.exit(1)  # 退出程序，因为核心依赖缺失
    try:
        import subprocess
        # 尝试安装 .whl 文件
        # 使用 --force-reinstall 可以确保即使版本冲突也能尝试安装
        # 使用 --no-deps 可以避免 pip 尝试解析并安装轮子自带的依赖（通常whl已经包含了所有必要组件）
        # 但是对于TensorRT，通常推荐让pip自行处理依赖，这里先不加 --no-deps
        install_command = [sys.executable, "-m", "pip", "install", tensorrt_whl_path]
        print(f"Running command: {' '.join(install_command)}")

        result = subprocess.run(install_command, capture_output=True, text=True, check=True)
        print("TensorRT installation successful!")
        print("Installation Output:")
        print(result.stdout)
        if result.stderr:
            print("Installation Errors/Warnings:")
            print(result.stderr)
        # 尝试再次导入以确认
        os.environ['PATH'] = os.environ['PATH'] + f';C:\TensorRT-*******\lib'
        import tensorrt as trt
        print(f"TensorRT successfully imported after installation. Version: {trt.__version__}")
        sys.exit(1)
    except subprocess.CalledProcessError as e:
        print(f"Error installing TensorRT: Command '{e.cmd}' returned non-zero exit status {e.returncode}.")
        print("Stdout:", e.stdout)
        print("Stderr:", e.stderr)
        sys.exit(1)  # 安装失败，退出程序
    except Exception as e:
        print(f"An unexpected error occurred during TensorRT installation: {e}")
        sys.exit(1)  # 发生其他错误，退出程序
# --- TensorRT 包检测和安装逻辑结束 ---
import cupy as cp
import cv2
from tqdm import tqdm, trange
from utils import transforms
from utils import dynamics
from utils.general import cell_struct, cell_list, channel_struct
from utils.GPU_Predictor_dynamic import GPUPredictorUtil
from utils.crop_main_body import crop_hole
from skimage.segmentation import find_boundaries
FILE = Path(__file__).absolute()
sys.path.append(FILE.parents[0].as_posix())
from loguru import logger as log

class CellPose(GPUPredictorUtil):
    _inference_lock = threading.Lock()
    def __init__(self, model_file_path):
        super().__init__()
        self.trt_logger = trt.Logger(trt.Logger.ERROR)
        self.engine = None
        self.model_file_path = model_file_path
        if not os.path.exists(self.model_file_path):
            log.error("model engine file is not exists in {}".format(self.model_file_path))
        else:
            log.info("loading model from {}".format(self.model_file_path))
        try:
            log.info(f"tensorrt version: {trt.__version__}")
            with open(self.model_file_path, "rb") as f, trt.Runtime(self.trt_logger) as runtime:
                self.engine = runtime.deserialize_cuda_engine(f.read())
            assert self.engine
        except:
            log.error("load model error",exc_info=True)
            from utils.export_trt import EngineBuilder
            if os.path.exists(self.model_file_path.replace(".engine", ".onnx")):
                log.info("find onnx model,try to load onnx model convert to engine")
                builder = EngineBuilder(verbose=False)
                builder.get_engine(self.model_file_path.replace(".engine", ".onnx"),self.model_file_path)
            else:
                log.error(f"{self.model_file_path.replace('.engine', '.onnx')} file is not exists,not convert model to engine,pls check")
            with open(self.model_file_path, "rb") as f, trt.Runtime(self.trt_logger) as runtime:
                self.engine = runtime.deserialize_cuda_engine(f.read())
        try:
            self.trt_version = int(trt.__version__.split(".")[0])
        except:
            self.trt_version = 8
            log.error(f"Failed to get TensorRT version:", exc_info=True)
        try:
            if self.max_concurrent_tasks==1:
                cuda.init()
                device = cuda.Device(0)
                self._shared_context = device.retain_primary_context()
                self._shared_context.push()
                self._shared_stream = cuda.Stream()
                log.info("单线程推理，创建共享上下文")
            else:
                log.info("多线程推理，创建独立上下文")
        except:
            self._shared_context=None
            self._shared_stream=None
            log.info("创建共享上下文失败,切换为独立上下文推理",exc_info=True)
        finally:
            if self._shared_context is not None:
                self._shared_context.pop()
            self._shared_execution_contexts["single_infer"]=True
        self.dynamic_mode,self.num_optimization_profiles,self.profile_shape = self._initialize_engine_info(self.engine)
        log.info(f"Initialization completed. dynamic: {self.dynamic_mode}, Profile shape: {self.profile_shape}, Num profiles: {self.num_optimization_profiles}")

    def _initialize_engine_info(self,engine):
        """
        Initializes dynamic mode, number of profiles, and profile shape
        after the engine has been successfully loaded.
        """
        if engine is None:
            log.error("Cannot initialize engine info: Engine is not loaded.")
            dynamic_mode = False
            num_optimization_profiles = 0
            profile_shape = None
            return dynamic_mode,num_optimization_profiles,profile_shape
        try:
            # Assuming input is always the first binding (index 0)
            input_name = engine.get_tensor_name(0)
            input_shape = engine.get_tensor_shape(input_name)
            dynamic_mode = -1 in tuple(input_shape)
            num_optimization_profiles = engine.num_optimization_profiles
            # Get shape from the first profile (assuming at least one profile exists for dynamic)
            if dynamic_mode and num_optimization_profiles > 0:
                 try:
                    engine_profile_shape = engine.get_tensor_profile_shape(profile_index=0, name=input_name)
                    # Profile shape is a tuple of (min_dims, opt_dims, max_dims) for the input tensor
                    min_dims = engine_profile_shape[0] # e.g., (1, 3, 640, 640)
                    opt_dims = engine_profile_shape[1] # e.g., (1, 3, 960, 960)
                    max_dims = engine_profile_shape[2] # e.g., (1, 3, 1280, 1280)
                    # Extracting H, W for simplicity, assuming Batch and Channel are fixed
                    profile_shape = [min_dims[-1], opt_dims[-1], max_dims[-1]] # e.g., [(640, 640), (960, 960), (1280, 1280)]
                 except:
                     log.error(f"Failed to get tensor profile shape for input {input_name}",exc_info=True)
                     profile_shape = None # Indicate failure
                     # Proceeding but note the potential issue
            elif not dynamic_mode:
                 # For static mode, the shape is just the engine's shape
                 profile_shape = [input_shape[-1],input_shape[-1],input_shape[-1]] # e.g., [(640, 640)] for static
            else:
                # Dynamic mode but no profiles (unlikely for a dynamic engine)
                profile_shape = None # Indicate issue
        except:
            log.error("Error during engine info initialization",exc_info=True)
            # Reset attributes on failure
            dynamic_mode = False
            num_optimization_profiles = 0
            profile_shape = None
        return dynamic_mode,num_optimization_profiles,profile_shape
    # 修改：分配缓冲区，接受 context 和 engine 作为参数
    def allocate_buffers(self, engine: trt.ICudaEngine, context: trt.IExecutionContext, input_shape: tuple):
        """为给定的引擎、上下文和输入形状分配 CUDA 缓冲区。"""
        inputs, outputs, allocations, bindings_ptr = [], [], [], []
        # Determine if the model is dynamic by checking the first binding (assuming input is binding 0)
        is_dynamic = False
        name=None
        if engine.num_io_tensors > 0:
            try:
                input_name = engine.get_tensor_name(0)
                if engine.get_tensor_mode(input_name) == trt.TensorIOMode.INPUT:
                    is_dynamic = -1 in tuple(engine.get_tensor_shape(input_name))
            except Exception as e:
                log.warning(f"Failed to check binding 0 dynamism: {e}. Proceeding assuming not dynamic.", exc_info=True)
                is_dynamic = False  # Default to not dynamic if check fails
        for i in range(engine.num_io_tensors):
            try:
                name = engine.get_tensor_name(i)
                dtype = engine.get_tensor_dtype(name)
                np_dtype = trt.nptype(dtype)
                is_input = engine.get_tensor_mode(name) == trt.TensorIOMode.INPUT
                current_engine_shape = tuple(engine.get_tensor_shape(name))
                if is_input and is_dynamic:  # If this is a dynamic input binding
                    # Check if input_shape matches the binding's expected dimensions (excluding batch)
                    # assuming input_shape is (N, C, H, W) and binding is (B, C, H, W)
                    expected_input_dims = current_engine_shape[1:]  # C, H, W or C, -1, -1 etc.
                    if len(input_shape[1:]) != len(expected_input_dims):
                        raise ValueError(
                            f"输入形状 {input_shape} 维度与引擎绑定 {i} ('{name}') 的期望维度 {current_engine_shape} 不匹配")
                    # Set input shape for dynamic inputs
                    # Use set_input_shape with the actual tensor name
                    if not context.set_input_shape(name, tuple(input_shape)):
                        # This might happen if input_shape is outside the optimization profile range
                        log.error(
                            f"Failed to set binding shape for input {i} ('{name}') to {input_shape}. Check optimization profiles.")
                        raise RuntimeError(f"Failed to set dynamic input shape for '{name}'")
                    # Get the actual shape the context is using after setting
                    shape = tuple(context.get_tensor_shape(name))
                    if not all(s > 0 for s in shape):
                        log.error(f"设置动态形状 {input_shape} 后绑定 {i} ('{name}') 的形状 {shape} 无效")
                        raise RuntimeError(f"Invalid shape {shape} after setting dynamic input shape for '{name}'")
                else:  # Static input or any output binding
                    # Get the shape from the context. For static bindings, this is the engine's shape.
                    # For outputs, this is the shape determined by the input shape for dynamic models.
                    shape = tuple(context.get_tensor_shape(name))
                    if not all(s > 0 for s in shape):
                        # This might happen if the dynamic input shape wasn't set correctly,
                        # leading to indeterminate output shapes.
                        log.error(f"绑定 {i} ('{name}') 的形状 {shape} 无效. 检查输入形状设置或优化配置.")
                        raise RuntimeError(f"Invalid shape {shape} for binding '{name}'.")
                size = np.dtype(np_dtype).itemsize
                for s in shape:
                    size *= s
                if size <= 0:
                    raise ValueError(f"计算出的绑定 {i} ('{name}') 的内存大小为 {size} 字节，无效")
                allocation = cuda.mem_alloc(int(size))
                binding = {'index': i, 'name': name, 'dtype': np_dtype, 'shape': list(shape), 'allocation': allocation}
                allocations.append(allocation)
                bindings_ptr.append(int(allocation))
                if is_input:
                    inputs.append(binding)
                else:
                    outputs.append(binding)
            except:
                log.error(f"分配或获取绑定 {i} ('{name}') 形状时出错",exc_info=True)
                # Clean up any allocated memory
                for alloc in allocations:
                    if alloc:
                        try:
                            alloc.free()
                        except:
                            pass
                raise  # Re-raise the exception
        if not inputs: raise RuntimeError("未能正确分配输入缓冲区")
        if not outputs: raise RuntimeError("未能正确分配输出缓冲区")
        return inputs, outputs, allocations, bindings_ptr
    def _tensorrt_infer(self,input_image: np.ndarray):
        """
        执行 TensorRT 推理。简化版，处理单 Profile 引擎的线程安全。
        需要输入图像形状为 (1, C, H, W)。
        """
        context = None
        inputs, outputs, allocations, bindings_ptr = None, None, None, None
        thread_name = threading.current_thread().name
        engine = self.engine
        dynamic_mode = self.dynamic_mode
        num_optimization_profiles = self.num_optimization_profiles

        if engine is None:
            log.error(f"[{thread_name}] Inference skipped: Engine not loaded.")
            return None
        # !!! 添加 CUDA Context 管理 !!!
        if (self.max_concurrent_tasks == 1) and self._shared_stream and self._shared_context:
            # 单线程模式，使用共享资源
            if self._shared_execution_contexts.get("single_infer") is not None:
                model_type="share_exec_context"
            context = self._shared_execution_contexts.get(model_type,None)
            if context is None:
                context=engine.create_execution_context()
                self._shared_execution_contexts[model_type]=context
            ctx = self._shared_context  # 只是引用共享 Context
            ctx.push()  # 激活当前线程的 Context
            stream = self._shared_stream
            print(f"[{thread_name}] Using shared CUDA Context and Stream for {model_type}.")
        else:
            device = cuda.Device(0)  # 选择设备 0
            ctx = device.retain_primary_context() # 为当前线程创建 Context
            ctx.push()  # 激活当前线程的 Context
            stream=cuda.Stream()# 为当前线程创建一个 Stream
            context=None
        # --- Acquire Lock for Single Profile Engine ---
        # 如果引擎Profile数量小于等于1（通常是1），则加锁。
        # 多个Profile时，理想情况每个线程独占一个Profile，无需加锁。
        acquire_lock = (num_optimization_profiles <= 1) and (self.max_concurrent_tasks==1)
        try:
            if acquire_lock:
                #print(f"[{thread_name}] Acquiring inference lock...")
                self._inference_lock.acquire()
                #print(f"[{thread_name}] Inference lock acquired.")
            # 创建 Execution Context
            #print(f"[{thread_name}] Creating execution context...")
            # 创建 Context，不指定 Profile 即可
            if context is None:
                print(f"[{thread_name}] Creating execution context...")
                context = engine.create_execution_context()
                if not context:
                    raise RuntimeError("Failed to create TensorRT execution context.")
            if dynamic_mode and num_optimization_profiles > 0:
                 try:
                    # Here you might add logic to select the *best* profile index
                    # based on input_image.shape[-2:]. For now, hardcode profile 0.
                    profile_index_to_use = 0 # Or determined dynamically
                    if not context.set_optimization_profile_async(profile_index_to_use, stream.handle):
                         raise RuntimeError(f"Failed to set optimization profile {profile_index_to_use}.")
                    #print(f"[{thread_name}] Optimization profile {profile_index_to_use} set.")
                 except Exception as e:
                     log.error(f"[{thread_name}] Failed to set optimization profile: {e}", exc_info=True)
                     # This could make allocate_buffers fail if output shapes depend on the profile
                     raise RuntimeError("Failed to set optimization profile.") from e
            # 分配 GPU 缓冲区
            #print(f"[{thread_name}] Allocating buffers...")
            # allocate_buffers 函数需要获取引擎和 Context 来确定每个绑定的形状
            # 这里调用一个简化版的 allocate_buffers，它依赖于 Context 中已设置的形状
            inputs, outputs, allocations, bindings_ptr = self.allocate_buffers(engine, context,input_image.shape)

            if not inputs: raise RuntimeError("No input buffers allocated.")
            # outputs 列表可能为空，取决于模型
            # 将输入数据从 Host 复制到 Device
            input_binding = inputs[0] # 假设输入是第一个绑定
            try:
                 cuda.memcpy_htod_async(input_binding['allocation'], np.ascontiguousarray(input_image), stream)
                 #print(f"[{thread_name}] Input data copied to device (async).")
            except Exception as e:
                 log.error(f"[{thread_name}] Failed to copy input data to device: {e}", exc_info=True)
                 raise RuntimeError("Failed to copy input data to device.") from e
            # 执行推理
            #print(f"[{thread_name}] Executing inference (async)...")
            try:
                if self.trt_version == 8:
                    if not context.execute_async_v2(bindings=bindings_ptr, stream_handle=stream.handle):
                        raise RuntimeError("TRT context execution failed.")
                else:
                    if not context.execute_v2(bindings=bindings_ptr):
                        raise RuntimeError("TRT context execution failed.")
            except Exception as e:
                log.error(f"[{thread_name}] TensorRT execution failed: {e}", exc_info=True)
                raise RuntimeError("TRT context execution failed.") from e

            output = np.zeros(outputs[0]['shape'], dtype=outputs[0]['dtype'])
            cuda.memcpy_dtoh_async(output, outputs[0]['allocation'], stream)

            for y in outputs:
                if y['name'] == "output":
                    output = np.zeros(y['shape'], dtype=y['dtype'])
                    cuda.memcpy_dtoh_async(output, y['allocation'], stream)
                    break
                else:
                    try:
                        output = output[:, [1, 2, 0], :,:]  # vista "flows" with 3 channels, 1) foreground 2) dx flow 3) dy flow
                    except:
                        pass
            # !!! Synchronize the thread's stream to ensure data is ready on host !!!
            try:
                 #print(f"[{thread_name}] Synchronizing CUDA Stream...")
                 stream.synchronize()
            except Exception as e:
                 log.error(f"[{thread_name}] Failed to synchronize CUDA Stream: {e}", exc_info=True)
                 raise RuntimeError("Failed to synchronize CUDA Stream.") from e
            # Return the results (make copies to avoid issues if buffers are reused later)
            return output
        except Exception as e:
            # 捕获 try 块中可能未被内部 try 捕获的异常
            log.error(f"[{thread_name}] An error occurred during inference process: {e}", exc_info=True)
            return None # 发生任何错误都返回 None
        finally:
            # 释放线程独有的 Context 和 buffers
            if allocations:
                for alloc in allocations:
                    try:
                        if alloc: alloc.free()
                    except Exception as e:
                        print(f"-[{thread_name}] Error freeing buffer: {e}")
                #print(f"[{thread_name}] Buffers released.")
            if context:
                if not (self.max_concurrent_tasks==1):
                     try:
                        del context
                        #print(f"[{thread_name}] TensorRT Execution Context released.")
                     except Exception as e:
                         print(f"-[{thread_name}] Error deleting Context: {e}")
            if stream:
                if not (self.max_concurrent_tasks==1):
                    try:
                        del stream # Release the stream
                        #print(f"[{thread_name}] CUDA Stream released.")
                    except Exception as e:
                        print(f"-[{thread_name}] Error deleting Stream: {e}")
            try:
                ctx.pop()
                # 建议在线程结束后显式销毁 Context
                # ctx.detach() # detach 可以防止意外被其他线程清理
                # del ctx # 通常 pop 后就可以 del
                #print(f"[{thread_name}] CUDA Context popped and detached/released.")
            except Exception as e:
                print(f"-[{thread_name}] Error popping or releasing CUDA Context: {e}")
            if acquire_lock:
                if self._inference_lock.locked():
                     self._inference_lock.release()
                     #print(f"[{threading.current_thread().name}] Inference lock released.")
                else:
                    log.warning(f"[{threading.current_thread().name}] Inference lock was expected to be locked but wasn't in finally.")
            # --- Release Lock ---
            # 确保锁在 finally 块中总是被释放，前提是它被获取了
    def read_image(self, input_src,cut_info=None):
        jyh_img = cv2.imdecode(np.fromfile(input_src, dtype=np.uint8), cv2.IMREAD_COLOR)
        try:
            if "_cut.jpg" in input_src:
                img0 = cv2.imdecode(np.fromfile(input_src[:-8] + "_merge_src.jpg", dtype=np.uint8), cv2.IMREAD_COLOR)
            else:
                img0 = cv2.imdecode(np.fromfile(input_src, dtype=np.uint8), cv2.IMREAD_COLOR)
        except:
            img0 = jyh_img
            log.error("不存在均一化图,使用原图进行分析",exc_info=True)
        print("img0.shape", img0.shape)
        cutoutconfig = f"{os.sep.join(os.path.dirname(input_src).split(os.sep)[:-1])}\CutoutConfig.txt"
        if os.path.exists(
                cutoutconfig.replace('D:', 'E:').replace('d:', 'e:').replace('imageTemp', 'imagesource')):
            cutoutconfig = cutoutconfig.replace('D:', 'E:').replace('d:', 'e:').replace('imageTemp',
                                                                                        'imagesource')
        log.info(f"CutoutConfig.txt {cutoutconfig.replace('imageTemp', 'imagesource')} 是否存在")
        try:
            if os.path.exists(cutoutconfig.replace('imageTemp', 'imagesource')):
                with open(cutoutconfig.replace('imageTemp', 'imagesource'), "r") as f:
                    cut_info = f.read()
            else:
                log.info("不存在CutoutConfig.txt路径,使用传入的cut_info参数")
            if isinstance(cut_info, str):
                cut_info = cut_info.split(",")
            else:
                raise ValueError(f"cut_info {cut_info} not correct")
            log.info(f"cut info {cut_info}")
            with self._inference_lock:
                crop_hole_mask = crop_hole(img0, kong_num=cut_info[0], lens=cut_info[1],
                                           view_judge=int(cut_info[2]))
                crop_hole_image = cv2.bitwise_and(img0, img0, mask=crop_hole_mask)
        except Exception as e:
            crop_hole_mask = np.ones((img0.shape[0], img0.shape[1]), dtype=np.uint8)
            crop_hole_image = img0
            log.info(f"crop hole fail,{e}")
        return jyh_img, img0, crop_hole_mask, crop_hole_image, img0.shape

    def run_tiled(self,
                  imgi,
                  batch_size=1,
                  nclasses=3,
                  augment=False,
                  bsize=224,
                  tile_overlap=0.1,
                  return_conv=False,
                  ):
        IMG, ysub, xsub, Ly, Lx = transforms.make_tiles(imgi, bsize=bsize, augment=augment, tile_overlap=tile_overlap)
        ny, nx, nchan, ly, lx = IMG.shape
        IMG = np.reshape(IMG, (ny * nx, nchan, ly, lx))
        batch_size = batch_size
        niter = int(np.ceil(IMG.shape[0] / batch_size))
        nout = nclasses + 32 * return_conv
        y = np.zeros((IMG.shape[0], nout, ly, lx))
        for k in tqdm(range(niter)):
            irange = slice(batch_size * k, min(IMG.shape[0], batch_size * k + batch_size))
            y0 = self._tensorrt_infer(IMG[irange])
            y[irange] = y0.reshape(irange.stop - irange.start, y0.shape[-3], y0.shape[-2], y0.shape[-1])
        if augment:
            y = np.reshape(y, (ny, nx, nout, bsize, bsize))
            y = transforms.unaugment_tiles(y, False)
            y = np.reshape(y, (-1, nout, bsize, bsize))

        yf = transforms.average_tiles(y, ysub, xsub, Ly, Lx)
        yf = yf[:, :imgi.shape[1], :imgi.shape[2]]
        return yf

    def run_net(self,
                imgs, nclasses=3, augment=False, tile=True, tile_overlap=0.1, bsize=224,
                return_conv=False, net_avg=False
                ):
        if imgs.ndim == 4:
            # make image Lz x nchan x Ly x Lx for net
            imgs = np.transpose(imgs, (0, 3, 1, 2))
            detranspose = (0, 2, 3, 1)
            return_conv = False
        else:
            # make image nchan x Ly x Lx for net
            imgs = np.transpose(imgs, (2, 0, 1))
            detranspose = (1, 2, 0)

        # pad image for net so Ly and Lx are divisible by 4
        imgs, ysub, xsub = transforms.pad_image_ND(imgs)
        # slices from padding
        #         slc = [slice(0, self.nclasses) for n in range(imgs.ndim)] # changed from imgs.shape[n]+1 for first slice size
        slc = [slice(0, imgs.shape[n] + 1) for n in range(imgs.ndim)]
        slc[-3] = slice(0, nclasses + 32 * return_conv + 1)
        slc[-2] = slice(ysub[0], ysub[-1] + 1)
        slc[-1] = slice(xsub[0], xsub[-1] + 1)
        slc = tuple(slc)
        # run network
        if tile or augment or imgs.ndim == 4:
            y = self.run_tiled(imgs, augment=augment, bsize=bsize,
                                      tile_overlap=tile_overlap,
                                      return_conv=return_conv)
        else:
            imgs = np.expand_dims(imgs, axis=0)
            y = self._tensorrt_infer(imgs)
            y = y[0]

        # slice out padding
        y = y[slc]
        # transpose so channels axis is last again
        y = np.transpose(y, detranspose)

        return y

    def cellpose_predict(self, src_x, normalize=True, invert=False,
                         rescale=1.0, resample=True, diameter=None, scale_facer=1,
                         cellprob_threshold=0.0,
                         flow_threshold=0.4, min_size=15, tile_overlap=0.1,
                         interp=True, do_3D=False, stitch_threshold=0.0, channels=None, channel_axis=None,
                         z_axis=None, nclasses=3, nchan=2, net_avg=False, augment=False, tile=True, model_type=None,
                         use_gpu=True):
        log.info("开始cellpose")
        start_time = time.time()
        if channels is None:
            channels = [0, 0]
        
        if model_type == 'nuclei':
            diam_mean = 17.
        else:
            diam_mean = 30.
        x = src_x
        if scale_facer != 1:
            x = cv2.resize(src_x, None, fx=1 / scale_facer, fy=1 / scale_facer)
            log.info(f"{src_x.shape} -resize-> {x.shape}")
            mode_diameter = max(2,diameter / scale_facer)
            log.info(f"diameter {diameter} -resize-> {mode_diameter}")
            diameter = mode_diameter
        if max(src_x.shape)<644:
            x=cv2.resize(src_x, (672,672),interpolation=cv2.INTER_NEAREST)
            log.info(f"max shape <644 {src_x.shape} -resize-> {x.shape}")
        x = transforms.convert_image(x, channels, channel_axis=channel_axis, z_axis=z_axis,
                                     do_3D=(do_3D or stitch_threshold > 0),
                                     normalize=False, invert=False, nchan=nchan)
        if x.ndim < 4:
            x = x[np.newaxis, ...]
        if diameter is not None and diameter > 0:
            rescale = diam_mean / diameter
        elif rescale is None:
            diameter = diam_mean
            rescale = diam_mean / diameter
        log.info(f"diameter: {diameter}, rescale: {rescale} diam_mean {diam_mean} useGPU {use_gpu} flow_threshold {flow_threshold}")
        rescale=min(rescale/scale_facer,rescale)
        log.info(f"remodify rescale: {rescale}")
        mod_flow_threshold=min(1.0,max(0.0,flow_threshold))
        if mod_flow_threshold!=flow_threshold:
            flow_threshold=mod_flow_threshold
            log.info(f"mode flow_threshold {flow_threshold}")
        shape = x.shape
        nimg = shape[0]
        bsize = self.profile_shape[0]
        log.info(f"bsize {bsize}")
        iterator = trange(nimg) if nimg > 1 else range(nimg)
        if resample:
            dP = np.zeros((2, nimg, shape[1], shape[2]), np.float32)
            cellprob = np.zeros((nimg, shape[1], shape[2]), np.float32)

        else:
            dP = np.zeros((2, nimg, int(shape[1] * rescale), int(shape[2] * rescale)), np.float32)
            cellprob = np.zeros((nimg, int(shape[1] * rescale), int(shape[2] * rescale)), np.float32)

        bd = np.zeros_like(cellprob)
        for i in iterator:
            img = np.asarray(x[i])
            if normalize or invert:
                img = transforms.normalize_img(img, invert=invert)
            if rescale != 1.0:
                img = transforms.resize_image(img, rsz=rescale)
            log.info(f"rescale {img.shape}")
            yf = self.run_net(img,bsize=bsize, net_avg=net_avg,
                                     augment=augment, tile=tile,
                                     tile_overlap=tile_overlap)
            if resample:
                yf = transforms.resize_image(yf, shape[1], shape[2])

            cellprob[i] = yf[:, :, 2]
            dP[:, i] = yf[:, :, :2].transpose((2, 0, 1))
            if nclasses == 4:
                if i == 0:
                    bd = np.zeros_like(cellprob)
                bd[i] = yf[:, :, 3]
        del yf
        niter = 200 if (do_3D and not resample) else (1 / rescale * 200)
        masks, p = [], []
        resize = [shape[1], shape[2]] if not resample else None

        for i in iterator:
            outputs = dynamics.compute_masks(dP[:, i], cellprob[i], niter=niter, cellprob_threshold=cellprob_threshold,
                                             flow_threshold=flow_threshold, interp=interp, resize=resize,
                                             use_gpu=use_gpu)
            masks.append(outputs[0])
            p.append(outputs[1])

        masks = np.array(masks)
        p = np.array(p)
        if stitch_threshold > 0 and nimg > 1:
            masks = transforms.stitch3D(masks, stitch_threshold=stitch_threshold)
            masks = transforms.fill_holes_and_remove_small_masks(masks, min_size=min_size)
        masks, dP, cellprob, p = masks.squeeze(), dP.squeeze(), cellprob.squeeze(), p.squeeze()
        if flow_threshold>0.4 or flow_threshold==0:
            cell_num=len(np.unique(masks))-1
            if cell_num<=0:
                cell_num=1
            object_means=np.sum(masks>1)/cell_num
            masks=remove_small_objects(masks, min_size=int(object_means*0.15), connectivity=1)
            log.info(f"start object area {object_means} filter")
        try:
            estimated_diam = dynamics.diameters_cpu(masks)[0]
            estimated_diam = diameter if (estimated_diam == 0 or np.isnan(estimated_diam)) else estimated_diam
            estimated_diam = np.maximum(5.0, estimated_diam)
            log.info(f"input diames {diameter},estimated diameter of cells {estimated_diam} pixels")
        except:
            log.info("error computing estimated diameter",exc_info=True)
        outlines = find_boundaries(masks, mode="inner")
        outY, outX = np.nonzero(outlines)
        # -1是轮廓,0是背景,1开始是细胞
        masks = masks.astype(np.int32)
        masks[outY, outX] = np.array([-1])
        if masks.shape[0] != src_x.shape[0]:
            masks = cv2.resize(masks, (src_x.shape[1], src_x.shape[0]), interpolation=cv2.INTER_NEAREST)
        print(masks.shape, src_x.shape)
        log.info("结束cellpose")
        log.info(f"cost time {time.time() - start_time} s")
        return masks

    def color_marker(self, markersend):
        markers_cp = markersend.copy()
        markers_cp = cp.array(markers_cp)
        num_markers = cp.unique(markers_cp).size - 1
        if num_markers > 0:
            colors_cp = cp.random.randint(0, 255, size=(num_markers, 3))
            color_mask_cp = colors_cp[markers_cp]
            clor_mask = color_mask_cp.get()
        else:
            clor_mask = np.zeros((markersend.shape[0], markersend.shape[1], 3), dtype=np.uint8)
        clor_mask[markersend == -1] = [255, 255, 255]
        clor_mask[markersend == 0] = [0, 0, 0]
        return clor_mask.astype(np.uint8)

    def get_color_mask(self, markersend, custome_color=None):
        color_mask = np.zeros((markersend.shape[0], markersend.shape[1], 3), dtype=np.uint8)
        if custome_color:
            color_mask[markersend >0] = custome_color  #
            color_mask[markersend == -1] = [255, 255, 255]
        else:
            color_mask = self.color_marker(markersend)
        return color_mask

    def brightened_image(self, img, channel=0):
        channels = cv2.split(img)
        # 提取指定通道,并merge成三通道
        merge_img = cv2.merge([channels[channel], channels[channel], channels[channel]])
        return merge_img


    def save_tiff_mask(self, mask_save_path, all_cell_masks, img_shape=None, channel="BF", compression_level=5,
                       bigtiff=False):
        """保存细胞掩码为TIFF文件，支持批处理和进度显示"""

        try:
            start_time = time.time()
            # 确保目录存在
            os.makedirs(os.path.dirname(mask_save_path), exist_ok=True)
            base_path, ext = os.path.splitext(mask_save_path)
            if ext.lower() in [".jpg", ".png", ".bmp", ".jpeg"]:
                mask_save_path = base_path
            if not mask_save_path.lower().endswith(".tif"):
                mask_save_path += "_multimasks.tif"
            total_cells = len(all_cell_masks)
            if total_cells == 0:
                log.warning(f"没有找到有效细胞掩码，跳过保存TIFF掩码: {mask_save_path}")
                return False
            log.info(f"开始保存 {total_cells} 个细胞掩码到 {mask_save_path}")
            # 创建TiffWriter对象，使用适当的参数
            if total_cells > 255:
                log.info("启用单页tiff保存")
                if len(img_shape) == 3:
                    img_shape = img_shape[:2]
                mask = np.zeros(img_shape, dtype=np.uint16)
                for cell_mask_info in all_cell_masks:
                    try:
                        tx, ty, bx, by = cell_mask_info["boxs"]
                        cell_index = cell_mask_info["cell_number"]
                        cell_mask = cell_mask_info["mask"].astype(np.uint16)
                        cell_mask *= cell_index
                        non_zero_indices = np.nonzero(cell_mask)
                        new_y_indices = non_zero_indices[0] + ty
                        new_x_indices = non_zero_indices[1] + tx
                        mask[new_y_indices, new_x_indices] = cell_mask[non_zero_indices]
                    except Exception as e:
                        print(e)
                        continue
                with tifffile.TiffWriter(mask_save_path, bigtiff=bigtiff) as obj_mask:
                    obj_mask.write(
                        mask,
                        metadata={'total_cells': total_cells, 'shape': img_shape, 'channels': channel},
                        compression="zlib",  # 使用zlib压缩,平衡速度和压缩率
                        compressionargs={'level': compression_level},  # 使用压缩级别5以提高速度
                        contiguous=True
                    )
            else:
                log.info("启用多页tiff保存")
                # 使用上下文管理器创建TiffWriter对象
                with tifffile.TiffWriter(mask_save_path, bigtiff=bigtiff) as obj_mask:
                    # 批量处理
                    for cell_mask_info in all_cell_masks:
                        obj_mask.write(
                            cell_mask_info["mask"],
                            metadata={'cell_number': cell_mask_info["cell_number"], 'boxs': cell_mask_info["boxs"],
                                      'channels': channel},
                            compression="zlib",  
                            compressionargs={'level': compression_level},  # 使用压缩级别5以提高速度
                        )
            # 计算并显示性能统计
            elapsed_time = time.time() - start_time
            file_size_mb = os.path.getsize(mask_save_path) / (1024 * 1024)
            log.info(f"TIFF掩码保存完成: {mask_save_path}")
            log.info(
                f"总耗时: {elapsed_time:.2f}秒, 文件大小: {file_size_mb:.2f}MB, 速度: {file_size_mb / elapsed_time:.2f}MB/s")
            return True
        except:
            log.error("保存TIFF掩码失败",exc_info=True)
            return False
    def get_cell_info(self, bf_jyh_img,img, masks, image_save_path, mask_save_path, custome_color=None, local_channel=0,msg_id=None,task_id=None):
        if custome_color is None:
            custome_color = [255, 0, 0]
        cell_infos = cell_list()
        cell_mask = masks.copy()
        cell_mask[cell_mask < 1] = 0
        labels = measure.label(cell_mask)
        props = measure.regionprops(labels, img)
        print(np.unique(cell_mask), np.unique(labels), len(props))
        channel_info = [None, None, None, None, None]
        log.info(f"find cell number {labels.max()}")
        try:
            self.send_response("MsgTime", msg_id,{"tid": task_id, "Time": int(labels.max() * 0.01) + 600})
        except:
            log.error("send MsgTime error",exc_info=True)
        bf_clor_mask = self.get_color_mask(masks, custome_color=custome_color)
        # print(bf_jyh_img.shape, bf_clor_mask.shape)
        bf_clor_map = cv2.add(bf_jyh_img, (bf_clor_mask * 0.5).astype(np.uint8))
        cv2.imencode(".jpg", bf_clor_map)[1].tofile(image_save_path)
        if mask_save_path is None or mask_save_path == "":
            mask_save_path = image_save_path[:-4]
        cell_number = 0
        obj_mask = []
        for index in range(0, labels.max()):
            (min_y, min_x, max_y, max_x) = props[index].bbox
            if int(props[index].area) < 10:
                continue
            cell_img = (img[props[index].slice]).astype(np.uint8)
            cell_gray = cv2.cvtColor(cell_img, cv2.COLOR_BGR2GRAY)
            maskinv = np.ones(cell_gray.shape, dtype="uint8")
            maskinv[props[index].image] = 0
            bg_gray = cv2.bitwise_and(cell_gray, cell_gray, mask=maskinv)
            # print(cell_img.shape,bg_gray.shape)
            channel_info[local_channel] = channel_struct(group_type=1,
                                                         local_channel=local_channel,
                                                         type=1, point_conuter=f"{min_x}:{min_y}|{max_x}:{max_y}",
                                                         major_axis_length=props[index].axis_major_length,
                                                         minor_axis_length=props[index].axis_minor_length).get_seg_info(
                cell_img=cell_img,
                cell_gray=cell_gray,
                bg_gray=bg_gray,
                center_x=int((int(min_x) + int(max_x)) / 2),
                center_y=int((int(min_y) + int(max_y)) / 2),
            )
            cell = cell_struct(BF=channel_info[0], FL1=channel_info[1], FL2=channel_info[2], FL3=channel_info[3])
            cell_infos.push_cell(cell)
            # 处理掩码数据
            mask = props[index].image.astype(np.uint8)
            obj_mask.append({'cell_number': cell_number, "mask": mask.copy(),
                             "boxs": [min_x, min_y, max_x, max_y]})
            cell_number += 1
        self.save_tiff_mask(mask_save_path,obj_mask,img_shape=img.shape)
        log.info(f"cell_infos info Number of Cell:{cell_infos.get_length()}")
        cell_infos.group_computer(local_channel)
        return cell_infos


    def predict(self,
                input_image_path,
                image_save_path,
                mask_save_path,
                data_save_path,
                fcs_save_path,
                cell_diameter,
                input_labelme_path="",
                scale_size=3661,
                flow_threshold=0.4,
                custome_color=None,
                cut_info = None,
                msg_id = None,
                task_id = None):
        if custome_color is None:
            custome_color = [255, 0, 0]
        log.info(f"处理input_img {input_image_path}")
        bf_jyh_img, bf_src, bf_hole_mask, bf_crop_image, origin_shape = self.read_image(input_image_path,cut_info=cut_info)
        bf_scale_facer = max(int(max(bf_crop_image.shape) / scale_size), 1)
        log.info(f"scale_size {scale_size} scale_facer {bf_scale_facer}")
        bf_markers=np.zeros_like(bf_hole_mask)
        if (input_labelme_path is not None) and os.path.exists(input_labelme_path):
            try:
                log.info(f"use {input_labelme_path} result computer info")
                with open(input_labelme_path, "r") as f:
                    labelme_json = json.load(f)
                shapes = labelme_json["shapes"]
                mask = np.zeros(bf_src.shape[:2], dtype=np.int32)
                mask = Image.fromarray(mask)
                draw = ImageDraw.Draw(mask)
                start_num = 2
                for i in range(len(shapes)):
                    if labelme_json["shapes"][i]["shape_type"] == "polygon":
                        points = labelme_json["shapes"][i]["points"]
                        xy = [tuple(point) for point in points]
                        draw.polygon(xy=xy, outline=-1, fill=start_num, width=2)
                        start_num += 1
                bf_markers = np.array(mask, dtype=np.int32)
            except:
                log.error(f"labelme_json error", exc_info=True)
        else:
            try:
                bf_markers = self.cellpose_predict(bf_crop_image, diameter=cell_diameter,flow_threshold=flow_threshold,
                                                    scale_facer=bf_scale_facer,channels=[0,0])
                #channels[0] # 0 - Grayscale, 3 - Blue, 2 - Green, 1 - Red
                #channels[1] # 0 - None, 3 - Blue, 2 - Green, 1 - Red
            except:
                log.error("cellpose predict error",exc_info=True)
        try:
            cell_infos=self.get_cell_info(bf_jyh_img,bf_src, bf_markers,image_save_path, mask_save_path,custome_color=custome_color,msg_id=msg_id,task_id=task_id)
            cell_infos.to_csv(data_save_path)
            cell_infos.to_csv(fcs_save_path)
        except:
            log.error("cellsegments error",exc_info=True)

if __name__ == '__main__':
    model = CellPose(model_file_path=r"cellsegments_APP\cellpose_model\leiqigaun_cellpose_dataset_sam_l.engine")
    for input_path in list_images(r"H:\类器官AOPI数据用例-230109\BF"):
        if "result" in input_path:
            continue
        i=50
        model.predict(input_image_path=input_path, cell_diameter=i, image_save_path=input_path[:-4]+f"_sam_l_result_{i}.jpg",data_save_path=input_path[:-4]+"_result.csv",fcs_save_path=input_path[:-4]+"_fcs_result.csv",mask_save_path=input_path[:-4]+f"_result_mask_{i}.png")
        break