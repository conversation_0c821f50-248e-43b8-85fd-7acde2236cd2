# UTF-8
VSVersionInfo(
  ffi=FixedFileInfo(
#filevers和prodvers应该始终是包含四个项的元组:（1、2、3、4）,将不需要的项设置为0
filevers=(1, 0, 0, 3),  # 文件版本******,鼠标悬浮exe会显示,也显示在 详细信息-文件版本,这个是检测版本的依据
prodvers=(1, 0, 0, 3), # 生产商,未见显示在哪里
mask=0x3f, # 两个位掩码
flags=0x0,
OS=0x4, # 为其设计此文件的操作系统,0x4-NT,无需更改它
fileType=0x1, # 文件的常规类型,0x1-该文件是一个应用程序
subtype=0x0, # 文件的功能,0x0表示该文件类型未定义
date=(0, 0) # 创建日期和时间戳
),
  kids=[
StringFileInfo(
  [
  StringTable(
    u'040904B0',
    [StringStruct(u'CompanyName', u'上海睿钰生物科技有限公司'), # 鼠标悬浮exe会显示
    StringStruct(u'FileDescription', u'AI类器官算法(肝胆管)'),    # 文件说明,鼠标悬浮exe会显示,也会显示在 详细信息-文件说明
    StringStruct(u'FileVersion', u'1.0'), # 没见哪里显示
    StringStruct(u'InternalName', u'1.0.0'),
    StringStruct(u'LegalCopyright', u'上海睿钰生物科技有限公司'), #版权,会显示在 详细信息-版权
    StringStruct(u'OriginalFilename', u'mq_main_organoid.exe'), #原始文件名,会显示在 详细信息-原始文件名
    StringStruct(u'ProductName', u'AI细胞图像分析软件-二期软件版人工修正版_muti_softwaredraw'),      #产品名称,会显示在 详细信息-产品名称
    StringStruct(u'ProductVersion', u'1.0.0')])    #产品版本,会显示在 详细信息-产品版本
  ]),
VarFileInfo([VarStruct(u'Translation', [2052, 1200])]) # 语言,中文简体
  ]
)