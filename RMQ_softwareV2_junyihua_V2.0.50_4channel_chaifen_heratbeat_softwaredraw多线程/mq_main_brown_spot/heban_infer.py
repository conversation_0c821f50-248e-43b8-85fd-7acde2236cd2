import json
import os
import threading
os.environ["OPENCV_IO_MAX_IMAGE_PIXELS"] = str(pow(2, 50))
from imutils.paths import list_images
from tqdm import tqdm
import numpy as np
import pycuda.driver as cuda #cuda和tensorrt放在cupy之前,否则运行会出错
import tensorrt as trt
import cv2
from utils.GPU_Predictor_dynamic import GPUPredictorUtil
from utils.crop_main_body import crop_hole
from sahi.slicing import slice_image
from lsnms import nms as large_nms
import time
from PIL import ImageFile
from loguru import logger as log
from PIL import Image
ImageFile.LOAD_TRUNCATED_IMAGES = True
Image.MAX_IMAGE_PIXELS = None
def convert_from_cv2_to_image(img: np.ndarray) -> Image:
    return Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))


def convert_from_image_to_cv2(img: Image) -> np.ndarray:
    return cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)

class HebanInfer(GPUPredictorUtil):
    _inference_lock = threading.Lock()
    def __init__(self, model_file_path):
        super().__init__()
        self.trt_logger = trt.Logger(trt.Logger.ERROR)
        self.engine = None
        self.model_file_path = model_file_path
        if not os.path.exists(self.model_file_path):
            log.error("model engine file is not exists in {}".format(self.model_file_path))
        else:
            log.info("loading model from {}".format(self.model_file_path))
        try:
            log.info(f"tensorrt version: {trt.__version__}")
            with open(self.model_file_path, "rb") as f, trt.Runtime(self.trt_logger) as runtime:
                self.engine = runtime.deserialize_cuda_engine(f.read())
            assert self.engine
        except:
            log.error("load model error",exc_info=True)
            from utils.export_trt import EngineBuilder
            if os.path.exists(self.model_file_path.replace(".engine", ".onnx")):
                log.info("find onnx model,try to load onnx model convert to engine")
                builder = EngineBuilder(verbose=False)
                builder.get_engine(self.model_file_path.replace(".engine", ".onnx"),self.model_file_path)
            else:
                log.error(f"{self.model_file_path.replace('.engine', '.onnx')} file is not exists,not convert model to engine,pls check")
            with open(self.model_file_path, "rb") as f, trt.Runtime(self.trt_logger) as runtime:
                self.engine = runtime.deserialize_cuda_engine(f.read())
        try:
            self.trt_version = int(trt.__version__.split(".")[0])
        except:
            self.trt_version = 8
            log.error(f"Failed to get TensorRT version:", exc_info=True)
        try:
            if self.max_concurrent_tasks==1:
                cuda.init()
                device = cuda.Device(0)
                self._shared_context = device.retain_primary_context()
                self._shared_context.push()
                self._shared_stream = cuda.Stream()
                log.info("单线程推理，创建共享上下文")
            else:
                log.info("多线程推理，创建独立上下文")
        except:
            self._shared_context=None
            self._shared_stream=None
            log.info("创建共享上下文失败,切换为独立上下文推理",exc_info=True)
        finally:
            if self._shared_context is not None:
                self._shared_context.pop()
            self._shared_execution_contexts["single_infer"]=True
        self.dynamic_mode,self.num_optimization_profiles,self.profile_shape = self._initialize_engine_info(self.engine)
        log.info(f"Initialization completed. dynamic: {self.dynamic_mode}, Profile shape: {self.profile_shape}, Num profiles: {self.num_optimization_profiles}")

    def _initialize_engine_info(self,engine):
        """
        Initializes dynamic mode, number of profiles, and profile shape
        after the engine has been successfully loaded.
        """
        if engine is None:
            log.error("Cannot initialize engine info: Engine is not loaded.")
            dynamic_mode = False
            num_optimization_profiles = 0
            profile_shape = None
            return dynamic_mode,num_optimization_profiles,profile_shape
        try:
            # Assuming input is always the first binding (index 0)
            input_name = engine.get_tensor_name(0)
            input_shape = engine.get_tensor_shape(input_name)
            dynamic_mode = -1 in tuple(input_shape)
            num_optimization_profiles = engine.num_optimization_profiles
            # Get shape from the first profile (assuming at least one profile exists for dynamic)
            if dynamic_mode and num_optimization_profiles > 0:
                 try:
                    engine_profile_shape = engine.get_tensor_profile_shape(profile_index=0, name=input_name)
                    # Profile shape is a tuple of (min_dims, opt_dims, max_dims) for the input tensor
                    min_dims = engine_profile_shape[0] # e.g., (1, 3, 640, 640)
                    opt_dims = engine_profile_shape[1] # e.g., (1, 3, 960, 960)
                    max_dims = engine_profile_shape[2] # e.g., (1, 3, 1280, 1280)
                    # Extracting H, W for simplicity, assuming Batch and Channel are fixed
                    profile_shape = [min_dims[-1], opt_dims[-1], max_dims[-1]] # e.g., [(640, 640), (960, 960), (1280, 1280)]
                 except:
                     log.error(f"Failed to get tensor profile shape for input {input_name}",exc_info=True)
                     profile_shape = None # Indicate failure
                     # Proceeding but note the potential issue
            elif not dynamic_mode:
                 # For static mode, the shape is just the engine's shape
                 profile_shape = [input_shape[-1],input_shape[-1],input_shape[-1]] # e.g., [(640, 640)] for static
            else:
                # Dynamic mode but no profiles (unlikely for a dynamic engine)
                profile_shape = None # Indicate issue
        except:
            log.error("Error during engine info initialization",exc_info=True)
            # Reset attributes on failure
            dynamic_mode = False
            num_optimization_profiles = 0
            profile_shape = None
        return dynamic_mode,num_optimization_profiles,profile_shape
    # 修改：分配缓冲区，接受 context 和 engine 作为参数
    def allocate_buffers(self, engine: trt.ICudaEngine, context: trt.IExecutionContext, input_shape: tuple):
        """为给定的引擎、上下文和输入形状分配 CUDA 缓冲区。"""
        inputs, outputs, allocations, bindings_ptr = [], [], [], []
        # Determine if the model is dynamic by checking the first binding (assuming input is binding 0)
        is_dynamic = False
        name=None
        if engine.num_io_tensors > 0:
            try:
                input_name = engine.get_tensor_name(0)
                if engine.get_tensor_mode(input_name) == trt.TensorIOMode.INPUT:
                    is_dynamic = -1 in tuple(engine.get_tensor_shape(input_name))
            except Exception as e:
                log.warning(f"Failed to check binding 0 dynamism: {e}. Proceeding assuming not dynamic.", exc_info=True)
                is_dynamic = False  # Default to not dynamic if check fails
        for i in range(engine.num_io_tensors):
            try:
                name = engine.get_tensor_name(i)
                dtype = engine.get_tensor_dtype(name)
                np_dtype = trt.nptype(dtype)
                is_input = engine.get_tensor_mode(name) == trt.TensorIOMode.INPUT
                current_engine_shape = tuple(engine.get_tensor_shape(name))
                if is_input and is_dynamic:  # If this is a dynamic input binding
                    # Check if input_shape matches the binding's expected dimensions (excluding batch)
                    # assuming input_shape is (N, C, H, W) and binding is (B, C, H, W)
                    expected_input_dims = current_engine_shape[1:]  # C, H, W or C, -1, -1 etc.
                    if len(input_shape[1:]) != len(expected_input_dims):
                        raise ValueError(
                            f"输入形状 {input_shape} 维度与引擎绑定 {i} ('{name}') 的期望维度 {current_engine_shape} 不匹配")
                    # Set input shape for dynamic inputs
                    # Use set_input_shape with the actual tensor name
                    if not context.set_input_shape(name, tuple(input_shape)):
                        # This might happen if input_shape is outside the optimization profile range
                        log.error(
                            f"Failed to set binding shape for input {i} ('{name}') to {input_shape}. Check optimization profiles.")
                        raise RuntimeError(f"Failed to set dynamic input shape for '{name}'")
                    # Get the actual shape the context is using after setting
                    shape = tuple(context.get_tensor_shape(name))
                    if not all(s > 0 for s in shape):
                        log.error(f"设置动态形状 {input_shape} 后绑定 {i} ('{name}') 的形状 {shape} 无效")
                        raise RuntimeError(f"Invalid shape {shape} after setting dynamic input shape for '{name}'")
                else:  # Static input or any output binding
                    # Get the shape from the context. For static bindings, this is the engine's shape.
                    # For outputs, this is the shape determined by the input shape for dynamic models.
                    shape = tuple(context.get_tensor_shape(name))
                    if not all(s > 0 for s in shape):
                        # This might happen if the dynamic input shape wasn't set correctly,
                        # leading to indeterminate output shapes.
                        log.error(f"绑定 {i} ('{name}') 的形状 {shape} 无效. 检查输入形状设置或优化配置.")
                        raise RuntimeError(f"Invalid shape {shape} for binding '{name}'.")
                size = np.dtype(np_dtype).itemsize
                for s in shape:
                    size *= s
                if size <= 0:
                    raise ValueError(f"计算出的绑定 {i} ('{name}') 的内存大小为 {size} 字节，无效")
                allocation = cuda.mem_alloc(int(size))
                binding = {'index': i, 'name': name, 'dtype': np_dtype, 'shape': list(shape), 'allocation': allocation}
                allocations.append(allocation)
                bindings_ptr.append(int(allocation))
                if is_input:
                    inputs.append(binding)
                else:
                    outputs.append(binding)
            except:
                log.error(f"分配或获取绑定 {i} ('{name}') 形状时出错",exc_info=True)
                # Clean up any allocated memory
                for alloc in allocations:
                    if alloc:
                        try:
                            alloc.free()
                        except:
                            pass
                raise  # Re-raise the exception
        if not inputs: raise RuntimeError("未能正确分配输入缓冲区")
        if not outputs: raise RuntimeError("未能正确分配输出缓冲区")
        return inputs, outputs, allocations, bindings_ptr

    def _tensorrt_infer(self,input_image: np.ndarray):
        """
        执行 TensorRT 推理。简化版，处理单 Profile 引擎的线程安全。
        需要输入图像形状为 (1, C, H, W)。
        """
        context = None
        inputs, outputs, allocations, bindings_ptr = None, None, None, None
        thread_name = threading.current_thread().name
        engine = self.engine
        dynamic_mode = self.dynamic_mode
        num_optimization_profiles = self.num_optimization_profiles

        if engine is None:
            log.error(f"[{thread_name}] Inference skipped: Engine not loaded.")
            return None,None
        # !!! 添加 CUDA Context 管理 !!!
        if (self.max_concurrent_tasks == 1) and self._shared_stream and self._shared_context:
            # 单线程模式，使用共享资源
            if self._shared_execution_contexts.get("single_infer") is not None:
                model_type="share_exec_context"
            context = self._shared_execution_contexts.get(model_type,None)
            if context is None:
                context=engine.create_execution_context()
                self._shared_execution_contexts[model_type]=context
            ctx = self._shared_context  # 只是引用共享 Context
            ctx.push()  # 激活当前线程的 Context
            stream = self._shared_stream
            print(f"[{thread_name}] Using shared CUDA Context and Stream for {model_type}.")
        else:
            device = cuda.Device(0)  # 选择设备 0
            ctx = device.retain_primary_context() # 为当前线程创建 Context
            ctx.push()  # 激活当前线程的 Context
            stream=cuda.Stream()# 为当前线程创建一个 Stream
            context=None
        # --- Acquire Lock for Single Profile Engine ---
        # 如果引擎Profile数量小于等于1（通常是1），则加锁。
        # 多个Profile时，理想情况每个线程独占一个Profile，无需加锁。
        acquire_lock = (num_optimization_profiles <= 1) and (self.max_concurrent_tasks==1)
        try:
            if acquire_lock:
                #print(f"[{thread_name}] Acquiring inference lock...")
                self._inference_lock.acquire()
                #print(f"[{thread_name}] Inference lock acquired.")
            # 创建 Execution Context
            #print(f"[{thread_name}] Creating execution context...")
            # 创建 Context，不指定 Profile 即可
            if context is None:
                print(f"[{thread_name}] Creating execution context...")
                context = engine.create_execution_context()
                if not context:
                    raise RuntimeError("Failed to create TensorRT execution context.")
            if dynamic_mode and num_optimization_profiles > 0:
                 try:
                    # Here you might add logic to select the *best* profile index
                    # based on input_image.shape[-2:]. For now, hardcode profile 0.
                    profile_index_to_use = 0 # Or determined dynamically
                    if not context.set_optimization_profile_async(profile_index_to_use, stream.handle):
                         raise RuntimeError(f"Failed to set optimization profile {profile_index_to_use}.")
                    #print(f"[{thread_name}] Optimization profile {profile_index_to_use} set.")
                 except Exception as e:
                     log.error(f"[{thread_name}] Failed to set optimization profile: {e}", exc_info=True)
                     # This could make allocate_buffers fail if output shapes depend on the profile
                     raise RuntimeError("Failed to set optimization profile.") from e
            # 分配 GPU 缓冲区
            #print(f"[{thread_name}] Allocating buffers...")
            # allocate_buffers 函数需要获取引擎和 Context 来确定每个绑定的形状
            # 这里调用一个简化版的 allocate_buffers，它依赖于 Context 中已设置的形状
            inputs, outputs, allocations, bindings_ptr = self.allocate_buffers(engine, context,input_image.shape)

            if not inputs: raise RuntimeError("No input buffers allocated.")
            # outputs 列表可能为空，取决于模型
            # 将输入数据从 Host 复制到 Device
            input_binding = inputs[0] # 假设输入是第一个绑定
            try:
                 cuda.memcpy_htod_async(input_binding['allocation'], np.ascontiguousarray(input_image), stream)
                 #print(f"[{thread_name}] Input data copied to device (async).")
            except Exception as e:
                 log.error(f"[{thread_name}] Failed to copy input data to device: {e}", exc_info=True)
                 raise RuntimeError("Failed to copy input data to device.") from e
            # 执行推理
            #print(f"[{thread_name}] Executing inference (async)...")

            try:
                if self.trt_version == 8:
                    if not context.execute_async_v2(bindings=bindings_ptr, stream_handle=stream.handle):
                        raise RuntimeError("TRT context execution failed.")
                else:
                    if not context.execute_v2(bindings=bindings_ptr):
                        raise RuntimeError("TRT context execution failed.")
            except Exception as e:
                log.error(f"[{thread_name}] TensorRT execution failed: {e}", exc_info=True)
                raise RuntimeError("TRT context execution failed.") from e
                # Prepare output buffers on Host
            pred_det = proto = None
            if len(outputs) < 2:
                pred_det = np.zeros(outputs[0]['shape'], dtype=outputs[0]['dtype'])
                try:
                    #print(f"[{thread_name}] Copying output data to host (async)...")
                    cuda.memcpy_dtoh_async(pred_det, outputs[0]['allocation'], stream)
                except Exception as e:
                    log.error(f"[{thread_name}] Failed to copy output data to host: {e}", exc_info=True)
                    raise RuntimeError("Failed to copy output data to host.") from e
            else:
                pred_det = np.zeros(outputs[1]['shape'], dtype=outputs[1]['dtype'])
                proto = np.zeros(outputs[0]['shape'], dtype=outputs[0]['dtype'])
                try:
                    #print(f"[{thread_name}] Copying output data to host (async)...")
                    cuda.memcpy_dtoh_async(pred_det, outputs[1]['allocation'], stream)
                    cuda.memcpy_dtoh_async(proto, outputs[0]['allocation'], stream)
                except Exception as e:
                    log.error(f"[{thread_name}] Failed to copy output data to host: {e}", exc_info=True)
                    raise RuntimeError("Failed to copy output data to host.") from e

            # !!! Synchronize the thread's stream to ensure data is ready on host !!!
            try:
                 #print(f"[{thread_name}] Synchronizing CUDA Stream...")
                 stream.synchronize()
            except Exception as e:
                 log.error(f"[{thread_name}] Failed to synchronize CUDA Stream: {e}", exc_info=True)
                 raise RuntimeError("Failed to synchronize CUDA Stream.") from e
            # Return the results (make copies to avoid issues if buffers are reused later)
            return pred_det, proto
        except Exception as e:
            # 捕获 try 块中可能未被内部 try 捕获的异常
            log.error(f"[{thread_name}] An error occurred during inference process: {e}", exc_info=True)
            return None,None # 发生任何错误都返回 None
        finally:
            # 释放线程独有的 Context 和 buffers
            if allocations:
                for alloc in allocations:
                    try:
                        if alloc: alloc.free()
                    except Exception as e:
                        print(f"-[{thread_name}] Error freeing buffer: {e}")
                #print(f"[{thread_name}] Buffers released.")
            if context:
                if not (self.max_concurrent_tasks==1):
                     try:
                        del context
                        #print(f"[{thread_name}] TensorRT Execution Context released.")
                     except Exception as e:
                         print(f"-[{thread_name}] Error deleting Context: {e}")
            if stream:
                if not (self.max_concurrent_tasks==1):
                    try:
                        del stream # Release the stream
                        #print(f"[{thread_name}] CUDA Stream released.")
                    except Exception as e:
                        print(f"-[{thread_name}] Error deleting Stream: {e}")
            try:
                ctx.pop()
                # 建议在线程结束后显式销毁 Context
                # ctx.detach() # detach 可以防止意外被其他线程清理
                # del ctx # 通常 pop 后就可以 del
                #print(f"[{thread_name}] CUDA Context popped and detached/released.")
            except Exception as e:
                print(f"-[{thread_name}] Error popping or releasing CUDA Context: {e}")
            if acquire_lock:
                if self._inference_lock.locked():
                     self._inference_lock.release()
                     #print(f"[{threading.current_thread().name}] Inference lock released.")
                else:
                    log.warning(f"[{threading.current_thread().name}] Inference lock was expected to be locked but wasn't in finally.")
            # --- Release Lock ---
            # 确保锁在 finally 块中总是被释放，前提是它被获取了

    def letterbox(self,im, new_shape=(640, 640), color=(114, 114, 114), auto=True, scalefill=False, scaleup=True, stride=32):
        # Resize and pad image while meeting stride-multiple constraints
        shape = im.shape[:2]  # current shape [height, width]
        if isinstance(new_shape, int):
            new_shape = (new_shape, new_shape)
        # Scale ratio (new / old)
        r = min(new_shape[0] / shape[0], new_shape[1] / shape[1])
        if not scaleup:  # only scale down, do not scale up (for better val mAP)
            r = min(r, 1.0)

        # Compute padding
        ratio = r, r  # width, height ratios
        new_unpad = int(round(shape[1] * r)), int(round(shape[0] * r))
        dw, dh = new_shape[1] - new_unpad[0], new_shape[0] - new_unpad[1]  # wh padding
        if auto:  # minimum rectangle
            dw, dh = np.mod(dw, stride), np.mod(dh, stride)  # wh padding
        elif scalefill:  # stretch
            dw, dh = 0.0, 0.0
            new_unpad = (new_shape[1], new_shape[0])
            ratio = new_shape[1] / shape[1], new_shape[0] / shape[0]  # width, height ratios

        dw /= 2  # divide padding into 2 sides
        dh /= 2
        if shape[::-1] != new_unpad:  # resize
            im = cv2.resize(im, new_unpad, interpolation=cv2.INTER_LINEAR)

        top, bottom = int(round(dh - 0.1)), int(round(dh + 0.1))
        left, right = int(round(dw - 0.1)), int(round(dw + 0.1))
        im = cv2.copyMakeBorder(im, top, bottom, left, right, cv2.BORDER_CONSTANT, value=color)  # add border
        return im, ratio, (dw, dh)

    def xywh2xyxy(self,x):
        # Convert nx4 boxes from [x, y, w, h] to [x1, y1, x2, y2] where xy1=top-left, xy2=bottom-right
        y = np.copy(x)
        y[:, 0] = x[:, 0] - x[:, 2] / 2  # top left x
        y[:, 1] = x[:, 1] - x[:, 3] / 2  # top left y
        y[:, 2] = x[:, 0] + x[:, 2] / 2  # bottom right x
        y[:, 3] = x[:, 1] + x[:, 3] / 2  # bottom right y
        return y

    def clip_coords(self,boxes, shape):
        # Clip bounding xyxy bounding boxes to image shape (height, width)
        boxes[:, [0, 2]] = boxes[:, [0, 2]].clip(0, shape[1])  # x1, x2
        boxes[:, [1, 3]] = boxes[:, [1, 3]].clip(0, shape[0])  # y1, y2
    def scale_coords(self,img1_shape, coords, img0_shape, ratio_pad=None):
        # Rescale coords (xyxy) from img1_shape to img0_shape
        if ratio_pad is None:  # calculate from img0_shape
            gain = min(img1_shape[0] / img0_shape[0], img1_shape[1] / img0_shape[1])  # gain  = old / new
            pad = (img1_shape[1] - img0_shape[1] * gain) / 2, (img1_shape[0] - img0_shape[0] * gain) / 2  # wh padding
        else:
            gain = ratio_pad[0][0]
            pad = ratio_pad[1]

        coords[:, [0, 2]] -= pad[0]  # x padding
        coords[:, [1, 3]] -= pad[1]  # y padding
        coords[:, :4] /= gain
        self.clip_coords(coords, img0_shape)
        return coords

    def computer_black(self,roi):
        if len(roi.shape) > 2:
            gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
            mask_flag = False
        else:
            gray = roi
            mask_flag = True
        height, width = roi.shape[:2]
        center_x = width // 2
        center_y = height // 2
        if cv2.countNonZero(gray) != 0:
            if gray[int(center_y), int(center_x)] < 1:
                return True
            else:
                gray_sum = gray.sum()
                if mask_flag:
                    if gray_sum < cv2.countNonZero(gray):
                        return True
                else:
                    if gray_sum < 5:
                        return True
                return False
        else:
            return True
    def preprocess(self, img_src_bgr, target_shape):
        image0 = img_src_bgr  # 使用局部变量
        if target_shape % 32 != 0:
            target_shape = int(target_shape / 32) * 32
        letterbox_img = self.letterbox(image0, target_shape, stride=64, auto=False)[0] # 获取 ratio 和 pad
        img = letterbox_img.transpose(2, 0, 1)[::-1]
        input_data = np.ascontiguousarray(img)  # 使用局部变量
        input_data = input_data.astype('float32') / 255.0
        if len(input_data.shape) == 3:
            input_data = input_data[None]
        # 返回预处理数据和用于后续缩放的信息
        return input_data, image0
    def box_area(self, box):
        # box = xyxy(4,n)
        return (box[2] - box[0]) * (box[3] - box[1])

    def box_iou(self, box1, box2, eps=1e-7):
        (a1, a2), (b1, b2) = np.split(box1[:, None], 2,
                                      axis=2), np.split(box2, 2, axis=1)
        array = np.minimum(a2, b2) - np.maximum(a1, b1)
        inter = array.clip(0)
        inter = inter.prod(2)

        # IoU = inter / (area1 + area2 - inter)
        return inter / (self.box_area(box1.T)[:, None] +
                        self.box_area(box2.T) - inter + eps)
    def non_max_suppression(self,
                            prediction,
                            conf_thres=0.25,
                            iou_thres=0.45,
                            nm=32,
                            wh_ratio_fliter=False):

        bs = prediction.shape[0]  # batch size
        nc = prediction.shape[2] - nm - 5  # number of classes
        xc = prediction[..., 4] > conf_thres  # candidates

        # Settings
        # min_wh = 2  # (pixels) minimum box width and height
        max_wh = 7680  # (pixels) maximum box width and height
        max_nms = 30000  # maximum number of boxes into cv2.dnn.NMSBoxes

        redundant = True  # require redundant detections
        merge = False  # use merge-NMS,False
        mi = 5 + nc  # mask start index,117中,前面是85（80类cls score, 4box, 1个obj score）,后面是32(mask coeffient)

        # numpy array不支持空数组的调用
        # https://blog.csdn.net/weixin_31866177/article/details/107380707
        # https://bobbyhadz.com/blog/python-indexerror-index-0-is-out-of-bounds-for-axis-0-with-size-0
        # https://blog.csdn.net/weixin_38753213/article/details/106754787
        # 不能对数组的元素赋值,比如 a=np.empty((0,6)),a[0]=1,这样会出错
        # output = np.zeros((0, 6 + nm), np.float32) * bs
        # 因此我们使用列表-》append-》转为numpy array
        output = []
        output_final = []
        for xi, x in enumerate(prediction):  # image index, image inference

            # confidence, xc的shape:(1, 25200), xi = 0, x的shape:(25200, 117)
            # 下面这句话就是筛选出obj score > conf_thres的instances
            # 经过筛选后的x的shape为:(44, 117)
            x = x[xc[xi]]

            # If none remain process next image
            if not x.shape[0]:
                continue

            # Compute conf
            x[:, 5:] *= x[:, 4:5]  # conf = obj_conf * cls_conf

            # Box/Mask
            # center_x, center_y, width, height) to (x1, y1, x2, y2)
            box = self.xywh2xyxy(x[:, :4])  # shape[44, 4]
            if wh_ratio_fliter:
                # 计算宽高比并过滤
                #log.info("启用长宽比过滤 aspect_ratio 2.1")
                w = box[:, 2] - box[:, 0]  # 宽度
                h = box[:, 3] - box[:, 1]  # 高度
                max_side = np.maximum(w, h)  # 最大边
                min_side = np.minimum(w, h)  # 最小边
                aspect_ratio = max_side / (min_side+1e-6)
                valid_indices = (aspect_ratio <= 2.1)
                x = x[valid_indices]
                box = box[valid_indices]
            # zero columns if no masks,从第index=85(第86个数)开始至117
            mask = x[:, mi:]  # mask shape[44, 32]

            # Detections matrix nx6 (xyxy, conf, cls)

            # best class only
            # x[:, 5:mi]是去除了4box + 1obj score的,就是cls score的从5到85
            # 下面这个max的第一个参数1,表示axis=1,就是按照列进行筛选cls中的最大值,且返回索引.
            # keepdim 表示是否需要保持输出的维度与输入一样,keepdim=True表示输出和输入的维度一样,
            # keepdim=False表示输出的维度被压缩了,也就是输出会比输入低一个维度.
            # j:Get the class with the highest confidence
            conf, j = x[:, 5:mi].max(axis=1), x[:, 5:mi].argmax(axis=1)
            conf, j = conf.reshape(-1, 1), j.reshape(-1, 1)

            # x的shape从[44, 38]经过conf.reshape(-1) > conf_thres筛选后变为
            # [43, 38],且:38 = 4box + 1conf + 1类别id + 32coeffients
            x = np.concatenate((box, conf, j.astype(float), mask),
                               axis=1)[conf.reshape(-1) > conf_thres]

            # Check shape
            n = x.shape[0]  # number of boxes
            if not n:  # no boxes
                continue
            elif n > max_nms:  # excess boxes
                x = x[(-x[:, 4]).argsort()[:max_nms]]  # sort by confidence
            else:
                x = x[(-x[:, 4]).argsort()]  # sort by confidence

            # Batched NMS
            c = x[:, 5:6] * max_wh  # classes
            # boxes (offset by class), scores
            boxes, scores = x[:, :4] + c, x[:, 4]
            # i = cv2.dnn.NMSBoxes(boxes.tolist(), scores.tolist(),
            #                      self.conf_thres, self.iou_thres)  # NMS
            i = large_nms(boxes, scores,score_threshold=conf_thres,iou_threshold=iou_thres)
            if merge and (1 < n <
                          3E3):  # Merge NMS (boxes merged using weighted mean)
                # update boxes as boxes(i,4) = weights(i,n) * boxes(n,4)
                iou = self.box_iou(boxes[i], boxes) > iou_thres  # iou matrix
                weights = iou * scores[None]  # box weights
                # equal tensor.float()
                # tt = np.dot(weights, x[:, :4]).astype(np.float32)

                x[i, :4] = np.dot(weights, x[:, :4]).astype(
                    np.float32) / weights.sum(1, keepdims=True)  # merged boxes
                if redundant:
                    i = i[iou.sum(1) > 1]  # require redundancy

            # output[xi] = x[i]
            output.append(x[i])
            # if (time.time() - t) > time_limit:
            #     print(f'WARNING ⚠️ NMS time limit {time_limit:.3f}s exceeded')
            #     break  # time limit exceeded

        output = np.array(output).reshape(-1, 6 + nm)
        output_final.append(output)

        return output_final

    def non_max_suppression_v8(self,
                               prediction,
                               conf_thres=0.25,
                               iou_thres=0.45,
                               nm=32,
                               wh_ratio_fliter=False):
        assert 0 <= conf_thres <= 1, f'Invalid Confidence threshold {conf_thres}, valid values are between 0.0 and 1.0'
        assert 0 <= iou_thres <= 1, f'Invalid IoU {iou_thres}, valid values are between 0.0 and 1.0'
        if isinstance(prediction,(list, tuple)):  # YOLOv8 model in validation model, output = (inference_out, loss_out)
            prediction = prediction[0]  # select only inference output
        bs = prediction.shape[0]  # batch size
        nc = prediction.shape[1] - nm - 4  # number of classes
        mi = 4 + nc  # mask start index
        # xc = prediction[:, 4:mi].max(1) > conf_thres  # candidates
        xc = prediction[:, 4:mi].max(1) > conf_thres
        # Settings
        # min_wh = 2  # (pixels) minimum box width and height

        redundant = True  # require redundant detections
        merge = False  # use merge-NMS
        max_wh = 7680  # (pixels) maximum box width and height
        max_nms = 30000  # maximum number of boxes into torchvision.ops.nms()
        # output = [torch.zeros((0, 6), device=prediction.device)] * bs
        output = [np.zeros((0, 6 + nm))] * bs
        for xi, x in enumerate(prediction):  # image index, image inference
            # Apply constraints
            # x[((x[..., 2:4] < min_wh) | (x[..., 2:4] > max_wh)).any(1), 4] = 0  # width-height
            x = np.transpose(x)[xc[xi]]  # confidence

            # If none remain process next image
            if not x.shape[0]:
                continue

            # Detections matrix nx6 (xyxy, conf, cls)
            box, cls, mask = x[:, :4], x[:, 4:nc + 4], x[:, nc + 4:]

            # Box (center x, center y, width, height) to (x1, y1, x2, y2)
            box = self.xywh2xyxy(box)
            if wh_ratio_fliter:
                # 计算宽高比并过滤
                # log.info("启用长宽比过滤 aspect_ratio 2.1")
                w = box[:, 2] - box[:, 0]  # 宽度
                h = box[:, 3] - box[:, 1]  # 高度
                max_side = np.maximum(w, h)  # 最大边
                min_side = np.minimum(w, h)  # 最小边
                aspect_ratio = max_side / (min_side + 1e-6)
                valid_indices = (aspect_ratio <= 2.1)
                x = x[valid_indices]
            box, cls, mask = x[:, :4], x[:, 4:nc + 4], x[:, nc + 4:]

            # Detections matrix nx6 (xyxy, conf, cls)
            conf = cls.max(1, keepdims=True)
            j_argmax = cls.argmax(1)
            j = j_argmax if j_argmax.shape == x[:, 5:].shape else \
                np.expand_dims(j_argmax, 1)  # for argmax(axis, keepdims=True)
            x = np.concatenate((box, conf, j.astype(np.float32), mask), 1)[conf.reshape(-1) > conf_thres]

            # Check shape
            n = x.shape[0]  # number of boxes
            if not n:  # no boxes
                continue
            # elif n > max_nms:  # excess boxes
            #     x_argsort = np.argsort(x[:, 4])[:max_nms] # sort by confidence
            #     x = x[x_argsort]
            x_argsort = np.argsort(x[:, 4])[::-1][:max_nms]  # sort by confidence
            x = x[x_argsort]
            # Batched NMS
            c = x[:, 5:6] * max_wh  # classes
            boxes, scores = x[:, :4] + c, x[:, 4]  # boxes (offset by class), scores

            #############################################
            # i = torchvision.ops.nms(boxes, scores, iou_thres)  # NMS
            i = large_nms(boxes, scores,score_threshold=conf_thres,iou_threshold=iou_thres)
            ############################################

            # if i.shape[0] > max_det:  # limit detections
            #     i = i[:max_det]
            if merge and (1 < n < 3E3):  # Merge NMS (boxes merged using weighted mean)
                # update boxes as boxes(i,4) = weights(i,n) * boxes(n,4)
                iou = self.box_iou(boxes[i], boxes) > iou_thres  # iou matrix
                weights = iou * scores[None]  # box weights
                # equal tensor.float()
                # tt = np.dot(weights, x[:, :4]).astype(np.float32)
                x[i, :4] = np.dot(weights, x[:, :4]).astype(np.float32) / weights.sum(1, keepdims=True)  # merged boxes
                if redundant:
                    i = i[iou.sum(1) > 1]  # require redundancy
            output[xi] = x[i]
        return output
    def clip_boxes(self, boxes, shape):
        # Clip boxes (xyxy) to image shape (height, width)
        if isinstance(boxes, np.ndarray):  # faster individually
            # np.array (faster grouped)
            boxes[:, [0, 2]] = boxes[:, [0, 2]].clip(0, shape[1])  # x1, x2
            boxes[:, [1, 3]] = boxes[:, [1, 3]].clip(0, shape[0])  # y1, y2
        else:
            print("type wont be supported")
    def scale_boxes(self, img1_shape, boxes, img0_shape, ratio_pad=None):
        # Rescale boxes (xyxy) from img1_shape to img0_shape
        if ratio_pad is None:  # calculate from img0_shape
            gain = min(img1_shape[0] / img0_shape[0],
                       img1_shape[1] / img0_shape[1])  # gain  = old / new
            pad = (img1_shape[1] - img0_shape[1] * gain) / 2, (
                    img1_shape[0] - img0_shape[0] * gain) / 2  # wh padding
        else:
            gain = ratio_pad[0][0]
            pad = ratio_pad[1]

        boxes[:, [0, 2]] -= pad[0]  # x padding
        boxes[:, [1, 3]] -= pad[1]  # y padding
        boxes[:, :4] /= gain
        self.clip_boxes(boxes, img0_shape)
        return boxes
    def get_prediction(self,image,
                       target_shape: int = None,
                       shift_amount=None,
                       conf_thres: float = 0.5,  # confidence threshold
                       iou_thres: float = 0.45,  # NMS IOU threshold
                       slice_pred=True
                       ):
        if shift_amount is None:
            shift_amount = [0, 0]
        preprocess_target_shape=target_shape
        if self.dynamic_mode:
            # 如果引擎是动态的，才使用调用者传入的 target_shape
            print(f"检测到引擎是动态的，使用请求的目标尺寸: {target_shape} self.profile_shape {self.profile_shape}")
            preprocess_target_shape = max(self.profile_shape[0],min(target_shape,self.profile_shape[2]))
        else:
            if target_shape != self.profile_shape[0]:
                print(f"检测到引擎是静态的 (固定尺寸 {self.profile_shape})。覆盖请求的目标尺寸 ({target_shape})。")
                preprocess_target_shape = self.profile_shape[0]
        net_input_data, original_image = self.preprocess(image,preprocess_target_shape)
        pred_det, proto = self._tensorrt_infer(net_input_data)
        if pred_det is None:
            log.error("Inference failed in _tensorrt_infer.")
            return []
        if proto is None:
            # print("pred_det.shape",pred_det.shape)
            output = []
            try:
                pred = self.non_max_suppression(pred_det,
                                                conf_thres,
                                                iou_thres, nm=0,wh_ratio_fliter=slice_pred)
            except Exception as e:
                log.info(f"{e}\n切换成v8解析")
                pred = self.non_max_suppression_v8(pred_det,
                                                   conf_thres,
                                                   iou_thres, nm=0,wh_ratio_fliter=slice_pred)
    
            for i, det in enumerate(pred):
                if len(det):
                    det[:, :4] = self.scale_boxes(net_input_data.shape[2:], det[:, :4], original_image.shape).round()
                    det[:, 0] = det[:, 0] + shift_amount[0]
                    det[:, 1] = det[:, 1] + shift_amount[1]
                    det[:, 2] = det[:, 2] + shift_amount[0]
                    det[:, 3] = det[:, 3] + shift_amount[1]
                    output.append(det)
            return output
        else:
            object_prediction = []
            try:
                pred = self.non_max_suppression(pred_det,
                                                conf_thres,
                                                iou_thres,wh_ratio_fliter=slice_pred)
            except Exception as e:
                log.info(f"{e}\n切换成v8解析")
                pred = self.non_max_suppression_v8(pred_det,
                                                   conf_thres,
                                                   iou_thres,wh_ratio_fliter=slice_pred)
    
            for i, det in enumerate(pred):
                if len(det):
                    # print('nms_det, nms_mask:', det.shape[0], proto[i].shape)
                    # Rescale boxes from img_size to im0 size
                    # 就是将当前在letterbox等处理后的图像上检测的box结果,映射返回原图大小上
                    old_det = det.copy()
                    proto_mask = [proto[i], net_input_data.shape[2:], original_image.shape, shift_amount]
                    det[:, :4] = self.scale_boxes(net_input_data.shape[2:], det[:, :4], original_image.shape).round()
                    det[:, 0] = det[:, 0] + shift_amount[0]
                    det[:, 1] = det[:, 1] + shift_amount[1]
                    det[:, 2] = det[:, 2] + shift_amount[0]
                    det[:, 3] = det[:, 3] + shift_amount[1]
                    # if full_pred:  # 切片时 整张图的conf
                    #     det[:, 4] = (det[:, 4] + 0.3).clip(0, 1)
                    # print(det.shape,len(protos_mask),protos_mask[0])
                    object_prediction.append([det, [proto_mask for _ in range(det.shape[0])], old_det])
            return object_prediction
    def detect(self,input_img,
               output_img_path,
               data_save_path,
               fcs_save_path,
               conf_threshold=0.2,
               iou_threshold=0.1,
               max_shape=1280,
               slice_pred_thed_max=4128,
               input_labelme_path="",
                cut_info = None,
                msg_id = None,
                task_id = None):

        conf_thres = conf_threshold
        iou_thres = iou_threshold
        log.info(f"conf_thres: {conf_thres} iou_thres: {iou_thres} max_shape {max_shape}")
        jyh_img = cv2.imdecode(np.fromfile(input_img, dtype=np.uint8), cv2.IMREAD_COLOR)
        try:
            if "_cut.jpg" in input_img:
                img_origin = cv2.imdecode(np.fromfile(input_img[:-8] + "_merge_src.jpg", dtype=np.uint8),
                                          cv2.IMREAD_COLOR)
            else:
                img_origin = cv2.imdecode(np.fromfile(input_img[:-4] + "_src.jpg", dtype=np.uint8),
                                          cv2.IMREAD_COLOR)
        except:
            img_origin = jyh_img
            log.info(f"未找到原图,使用均一化图计算")
        img_origin_shape = img_origin.shape[:2]
        # 抠图
        try:
            cutoutconfig = os.path.join(os.sep.join(os.path.dirname(input_img).split(os.sep)[:-1]), "CutoutConfig.txt")
            if os.path.exists(cutoutconfig.replace('D:', 'E:').replace('d:', 'e:').replace('imageTemp', 'imagesource')):
                cutoutconfig = cutoutconfig.replace('D:', 'E:').replace('d:', 'e:').replace('imageTemp',
                                                                                            'imagesource')
            log.info(f"检查CutoutConfig.txt {cutoutconfig.replace('imageTemp', 'imagesource')} 是否存在")
            if os.path.exists(cutoutconfig.replace('imageTemp', 'imagesource')):
                with open(cutoutconfig.replace('imageTemp', 'imagesource'), "r") as f:
                    cut_info = f.read()
            else:
                log.info("不存在CutoutConfig.txt路径,使用传入的cut_info参数")
            if isinstance(cut_info, str):
                cut_info = cut_info.split(",")
            else:
                raise ValueError(f"cut_info {cut_info} not correct")
            log.info(f"cut info {cut_info}")
            with self._inference_lock:
                crop_mask = crop_hole(img_origin, kong_num=cut_info[0], lens=cut_info[1],
                                      view_judge=int(cut_info[2]))
        except Exception as e:
            crop_mask = np.ones((img_origin.shape[0], img_origin.shape[1]), dtype=np.uint8)
            log.error(f"crop hole fail,{e}")
        # print(img_origin_shape,crop_hole_image.shape)
        perform_full_pred = True if max(img_origin_shape) < slice_pred_thed_max else False  # whole pic det
        log.info(f"perform_full_pred {perform_full_pred} slice_pred_thed_max {slice_pred_thed_max}")
        # plt.imshow(crop_hole_image)
        # plt.show()
        durations_in_seconds = dict()
        time_start = time.time()
        if (input_labelme_path is not None) and os.path.exists(input_labelme_path):
            end_det = np.empty([0, 5])
            end_segments=[]
            try:
                log.info(f"use {input_labelme_path} result computer info")
                with open(input_labelme_path, "r") as f:
                        labelme_json = json.load(f)
                shapes = labelme_json["shapes"]
                end_det = np.empty([len(shapes), 6])
                for i in range(len(shapes)):
                    class_id = 0
                    score = 1.0
                    if labelme_json["shapes"][i]["shape_type"] == "polygon":
                        points = labelme_json["shapes"][i]["points"]
                        end_segments.append(np.array(points).astype('float32'))
                        xs = [p[0] for p in points]
                        ys = [p[1] for p in points]
                        minx = min(xs)
                        maxx = max(xs)
                        miny = min(ys)
                        maxy = max(ys)
                        # 补充下面的代码实现添加shape的外部矩形到cell_info
                        end_det[i] = np.array([minx, miny, maxx, maxy,score, class_id])
            except:
                log.error("labelme_json error",exc_info=True)
        else:
            # perform sliced prediction
            object_prediction_list = []
            if perform_full_pred:
                # perform full prediction
                log.info('----------------------------------只进行整图预测---------------------------------')
                det_time = time.time()
                for size in tqdm(range(640, 4320, 320)):
                    prediction_result = self.get_prediction(
                        image=img_origin,
                        target_shape=size,
                        shift_amount=None,
                        conf_thres=conf_thres,
                        iou_thres=iou_thres,
                        slice_pred=False
                    )
                    object_prediction_list.extend(prediction_result)
            else:
                log.info('---------------------------------切片检测--------------------------------')
                slice_image_result = slice_image(
                    image=convert_from_cv2_to_image(img_origin),
                    slice_height=max_shape,
                    slice_width=max_shape,
                    overlap_height_ratio=0.2,
                    overlap_width_ratio=0.2
                )
                num_slices = len(slice_image_result)
                durations_in_seconds["slice"] = time.time() - time_start
                det_time = time.time()
                num_batch = 1
                log.info(f"Number of slices:{num_slices} num_batch {num_batch} input_size {max_shape}")
                # perform sliced prediction
                object_prediction_list = []
                for num_id in tqdm(range(0, num_slices, num_batch)):
                    image_list = slice_image_result.images[num_id:num_id + num_batch]
                    shift_amount_list = slice_image_result.starting_pixels[num_id:num_id + num_batch]
                    prediction_result = self.get_prediction(
                        image=convert_from_image_to_cv2(image_list[0]),
                        target_shape=max_shape,
                        shift_amount=shift_amount_list[0],
                        conf_thres=conf_thres,
                        iou_thres=iou_thres
                    )
                    object_prediction_list.extend(prediction_result)
                if num_slices > 1:
                    log.info('-------------------------------------执行切片后整图预测-------------------------------------')
                    for size in tqdm(range(640, 4320, 320)):
                        prediction_result = self.get_prediction(
                            image=img_origin,
                            target_shape=size,
                            shift_amount=None,
                            conf_thres=conf_thres,
                            iou_thres=iou_thres,
                            slice_pred=False
                        )
                        object_prediction_list.extend(prediction_result)
            detection_start = time.time()
            durations_in_seconds["detection"] = detection_start - det_time
            # remove empty predictions
            object_prediction_list = [object_prediction for object_prediction in object_prediction_list if object_prediction is not None]
            end_det = np.empty([0, 5])
            if len(object_prediction_list) > 0:
                det = np.concatenate([x[0] if isinstance(x, list) else x for x in object_prediction_list], axis=0)
                nms_start = time.time()
                log.info("开始执行nms")
                keep = large_nms(det[:, :4], det[:, 4], iou_threshold=iou_thres, score_threshold=conf_thres)
                end_det = det[keep]
                log.info("nms处理完成")
                log.info(f"Number of Cell:{det.shape[0]}")
                durations_in_seconds["NMS"] = time.time() - nms_start
        try:
            self.send_response("MsgTime", msg_id,{"tid": task_id, "Time": int(end_det.shape[0] * 1) + 600})
        except:
            log.error("send MsgTime error",exc_info=True)
        print(durations_in_seconds)
        # 绘图
        get_infostart = time.time()
        self.get_info(jyh_img,img_origin, end_det, output_img_path, data_save_path, fcs_save_path,crop_mask)
        get_infoend = time.time()
        durations_in_seconds["get_info"] = get_infoend - get_infostart
        durations_in_seconds["cost time"] = time.time() - time_start
        log.info(durations_in_seconds)

    def get_info(self,jyh_img, img, det, output_img_path, data_save_path, fcs_save_path,crop_mask):
        analysis_result_title = ','.join(['细胞编号', '属性值', '通道', 'X坐标', 'Y坐标', '团属性', '面积',
                                        '周长', '长轴', '短轴', '圆度', '边缘锐利度', '边缘差异率', '平均灰度',
                                        '累计灰度',
                                        '最大灰度', '最小灰度', '灰度差', '背景差异', '正圆率', '亮度', '轮廓']) + "\n"
        analysis_result = ""
        color_mask = np.zeros(img.shape, dtype=np.uint8)
        if len(det):
            boxes, scores, class_ids = det[:, :4], det[:, 4], det[:, 5]
            custom_color=(0,255,0)
            cell_number=0
            for cell_num, (bbox, score, class_id) in enumerate(zip(boxes, scores, class_ids)):
                x1, y1, x2, y2 = bbox.astype(int)
                if self.computer_black(crop_mask[y1:y2,x1:x2]):
                    continue
                ctype = 1
                local_channel = 0
                center_x = int((x1 + x2) / 2)
                center_y = int((y1 + y2) / 2)
                group_type = ""  # 团属性
                major_axis_length = max((x2 - x1), (y2 - y1))
                minor_axis_length = min((x2 - x1), (y2 - y1))
                area = 0
                try:
                    area = minor_axis_length * minor_axis_length
                    color_mask = cv2.rectangle(color_mask, (x1, y1), (x2, y2), custom_color, -1)
                except:
                    log.error("drow contour error",exc_info=True)
                res = [ctype, local_channel, center_x, center_y, group_type, int(area), "",
                       int(major_axis_length), int(minor_axis_length), "", "", "", "", "", "", "", "", "", "", "",f'{x1}:{y1}|{x2}:{y2}']
                analysis_result += str(cell_number + 1) + "," + ",".join(map(str, res)) + "\n"
                cell_number=cell_number+1
        color_mask = (color_mask * 0.5).astype(np.uint8)
        img = cv2.add(jyh_img, color_mask)
        del color_mask
        end_analysis_result = analysis_result_title + analysis_result
        try:
            if os.path.exists(data_save_path):
                os.remove(data_save_path)
            with open(data_save_path, 'a+', encoding='utf-8') as f:
                f.write(end_analysis_result)
        except:
            log.error(f"data_save_path {data_save_path} not exists", exc_info=True)
        try:
            if fcs_save_path != '':
                if os.path.exists(fcs_save_path):
                    os.remove(fcs_save_path)
                with open(fcs_save_path, 'a+', encoding='utf-8') as f:
                    f.write(end_analysis_result)
        except:
            log.error(f"fcs_save_path {fcs_save_path} not exists", exc_info=True)
        try:
            cv2.imencode('.jpg', img)[1].tofile(output_img_path)
        except:
            log.error("save image error",exc_info=True)


if __name__ == '__main__':
    #v5_detmodel = YoLov5TRT(r"G:\openvino_pyinstaller\RMQ_softwareV2_junyihua_V2.0.50_4channel_chaifen_aidraw\APP\brown_spot_APP\heban_det_0505\best.engine")
    v5_detmodel = HebanInfer(r"brown_spot_APP\褐斑_rec_230505_AI\best.engine")
    for impath in list_images(r"H:\suanfa_test"):
        if "result" in impath:
            continue
        v5_detmodel.detect(impath,
                           output_img_path=impath[:-4] + f"_result.jpg",
                           data_save_path=r"H:\sdasd.csv",
                           fcs_save_path=r"H:\sdasd.csv",
                           conf_threshold=0.05,
                           iou_threshold=0.1,
                           max_shape=2560,
                           slice_pred_thed_max=4128)
        #break
