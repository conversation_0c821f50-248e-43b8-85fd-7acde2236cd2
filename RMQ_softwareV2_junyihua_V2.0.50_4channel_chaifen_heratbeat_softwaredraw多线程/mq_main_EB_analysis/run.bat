@echo off

:: 切换到指定虚拟环境
call C:\Users\<USER>\.virtualenvs\openvino_pyinstaller-bMNptl9L\Scripts\activate.bat


:: 执行PyInstaller打包spec文件
:: 遍历spec文件打包
:: 记录当前路径
set old_path=%cd%

:: 切换到bat目录
cd /d %~dp0

echo %old_path%

for %%f in (*.spec) do (
  :: 切换到上级目录
  :: cd ..
  :: 执行pyinstaller打包
  :: pyinstaller -D %%~nf/%%~nxf --noconfirm
  pyinstaller -D %%~nxf --noconfirm
)
:: 完成打包后操作
echo 开始复制和清理...

:: 切换回原目录
cd /d %old_path%

:: 复制dist下的mq_main_开头的exe文件到当前目录
for /d %%d in ("%old_path%\dist\*") do (
  if exist "%%d\mq_main_*.exe" (
    copy "%%d\mq_main_*.exe" "%old_path%"
  )
)

:: 删除dist文件夹中所有dll文件
for /r "%old_path%\dist" %%a in (*.dll) do del "%%a"
:: 删除当前目录下的build和dist文件夹,并验证删除结果
rd /s /q "%old_path%\build" && echo Build folder deleted successfully. || echo Failed to delete build folder.
rd /s /q "%old_path%\dist" && echo Dist folder deleted successfully. || echo Failed to delete dist folder.
rd /s /q "%old_path%\__pycache__" && echo Dist folder deleted successfully. || echo Failed to delete dist folder.
:: 删除dist文件夹中所有dll文件
for /r "%old_path%\dist" %%a in (*.dll) do del "%%a"
rd /s /q "%old_path%\dist" && echo Dist folder deleted successfully. || echo Failed to delete dist folder.
:: 再次删除dist文件夹中所有dll文件
for /r "%old_path%\dist" %%a in (*.dll) do del "%%a"
rd /s /q "%old_path%\dist" && echo Dist folder deleted successfully. || echo Failed to delete dist folder.
:: 完成后退出虚拟环境
call C:\Users\<USER>\.virtualenvs\openvino_pyinstaller-bMNptl9L\Scripts\deactivate.bat