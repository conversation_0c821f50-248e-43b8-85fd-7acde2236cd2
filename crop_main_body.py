import math
import time

# import matplotlib.pyplot as plt
import os.path
import yaml
from shapely.geometry import Polygon  # 多边形


import numpy as np
import os
os.environ["OPENCV_IO_MAX_IMAGE_PIXELS"] = str(pow(2, 50))
import cv2
import logging

log = logging.getLogger("rmq_main.crop_hole")


def get_center_area(im_cv, img_bbox=None, img_crop=False):
    if img_crop:
        w_center, h_center = (img_bbox[0] + img_bbox[2]) // 2, (img_bbox[1] + img_bbox[3]) // 2
    else:
        im_h, im_w = im_cv.shape[:2]
        h_center, w_center = im_h // 2, im_w // 2
    center_l_x, center_l_y = w_center - 1000, h_center - 1000
    center_r_x, center_r_y = w_center + 1000, h_center + 1000
    center_area = [center_l_x, center_l_y, center_r_x, center_r_y]
    return center_area


def img_handle(kong, lens_mul, img_size,  kong_num):
    crop_ratio = 0.04
    # resize_ratio = 5
    # if kong_num == 1:
    #     param_r = 20
    # else:
    #     param_r = 30
    param_r = 20
    min_r = 400
    max_r = 650
    size_ratio = 1

    if kong == 25:
        # round([数字或表达式],[参数]).参数为正整数,则对小数部分四舍五入,为0则对整数部分四舍五入
        size_ratio = round(6700/img_size)
        if size_ratio <= 0:
            size_ratio = 1

        if lens_mul == 4:
            resize_ratio = 5 / size_ratio
            param_r = 20
            # min_r = 380
            # max_r = 550
            min_r = 480
            if kong_num == 1:
                max_r = 550  # 550  560
            else:
                max_r = 560
        else:  # 10
            resize_ratio = 5 / size_ratio
            param_r = 20
            min_r = 600
            max_r = 690

    elif kong == 21:  # 6 KONG have not 10x
        size_ratio = round(30500/img_size)
        if size_ratio <= 0:
            size_ratio = 1

        resize_ratio = 30 / size_ratio
        # param_r = 30
        if kong_num == 1:
            param_r = 30
        else:
            param_r = 40
        min_r = 450
        max_r = 550

    elif kong == 22:
        crop_ratio = 0.08
        size_ratio = round(20300/img_size)
        if size_ratio <= 0:
            size_ratio = 1

        if lens_mul == 4:
            resize_ratio = 20 / size_ratio
            # param_r = 20
            if kong_num == 1:
                param_r = 20
            else:
                param_r = 30
            min_r = 420
            max_r = 480

            # min_r = 450
            # max_r = 540
        else:  # 10
            # down resolution/2
            # resize_ratio = 45  # 200  280
            resize_ratio = 20 / size_ratio
            # param_r = 20
            if kong_num == 1:
                param_r = 20
            else:
                param_r = 30
            # min_r = 500
            # max_r = 640

            crop_ratio = 0.01
            min_r = 500
            max_r = 580
        # print(min_r, max_r)

    elif kong == 23:
        crop_ratio = 0.08
        size_ratio = round(15300/img_size)
        if size_ratio <= 0:
            size_ratio = 1

        if lens_mul == 4:
            resize_ratio = 15 / size_ratio
            if kong_num == 1:
                param_r = 20
            else:
                param_r = 30
            # min_r = 400
            # max_r = 480

            min_r = 400
            max_r = 460

            # max_r = 750
        else:
            crop_ratio = 0.02
            resize_ratio = 15 / size_ratio

            if kong_num == 1:
                param_r = 20
            else:
                param_r = 30
            min_r = 480
            max_r = 540

    elif kong == 24:
        # crop_ratio = 0.08
        size_ratio = round(11500/img_size)
        if kong_num == 1:
            param_r = 20
        else:
            param_r = 30

        if size_ratio <= 0:
            size_ratio = 1

        if lens_mul == 4:
            # crop_ratio = 0.08
            crop_ratio = 0.12
            resize_ratio = 10 / size_ratio

            min_r = 400
            max_r = 460
        else:
            crop_ratio = 0.02
            resize_ratio = 12 / size_ratio

            min_r = 400
            max_r = 465

    elif kong == 26:
        crop_ratio = 0.02
        size_ratio = round(4100/img_size)
        if size_ratio <= 0:
            size_ratio = 1

        if lens_mul == 4:
            resize_ratio = 3 / size_ratio
        else:
            # crop_ratio = 0.01
            resize_ratio = 7 / size_ratio
    else:
        crop_ratio, resize_ratio, param_r, min_r, max_r = 0, 0, 0, 0, 0

    # print('size_img:', size_ratio)
    resize_ratio = int(resize_ratio)
    # print('resize_ratio:', resize_ratio)
    img_result = [crop_ratio, resize_ratio, param_r, min_r, max_r]
    # print('resize:', resize_ratio)

    return img_result


def lightBalance(img, light=200):
    h, w, _ = img.shape
    res_img = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
    chv = res_img[:, :, 2]
    ratiov = cv2.resize(chv, (w // 8, h // 8)).astype(np.float)
    ratiov = cv2.blur(ratiov, (7, 7))
    np.clip(ratiov, 0.0001, 10000, out=ratiov)
    ratiov = cv2.resize(ratiov, (w, h))
    chv = (chv / ratiov) * light
    np.clip(chv, 0, 255, out=chv)
    res_img[:, :, 2] = chv.astype(np.uint8)
    res_img = cv2.cvtColor(res_img, cv2.COLOR_HSV2BGR)
    return res_img


def morphology_img(cv_im, resize_ratio, crop_ratio, kong, lens_mul, kong_num):
    img_h, img_w = cv_im.shape[:2]
    img_h_r, img_w_r = img_h // resize_ratio, img_w // resize_ratio

    cv_im = lightBalance(cv_im)
    cv_im = cv2.resize(cv_im, (img_w_r, img_h_r))

    # if kong == 23 and lens_mul == 4:
    if kong_num == 2 and kong != 25:
        cv_im = (cv_im*2-200).astype(np.uint8)
        np.clip(cv_im, 0, 255, out=cv_im)

    crop_h, crop_w = math.ceil(img_h_r * crop_ratio), math.ceil(img_w_r * crop_ratio)
    # print('crop:', crop_h, crop_w)

    # 高斯模糊
    s = time.time()
    blur = cv2.GaussianBlur(cv_im, (13, 13), 0)
    # print('g:', time.time() - s)
    gray = cv2.cvtColor(blur, cv2.COLOR_BGR2GRAY)

    s = time.time()
    # ker = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (9, 9))  # 定义结构性元素  椭圆
    num_rec = 5
    multiply_ratio = 10
    if kong == 26:
        num_rec = 9
    # if kong == 23 and lens_mul == 4:  # 7 9
    #     num_rec = 7
    #     multiply_ratio = 3
    # if kong == 26:
    #     num_rec = 9
    # if kong == 23: #2.46.2专用
    #     num_rec = 17

    # if kong_num == 2 and kong != 25:
    #     num_rec = 7
    #     multiply_ratio = 15

    ker = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (num_rec, num_rec))
    img_gradient = cv2.morphologyEx(gray, cv2.MORPH_GRADIENT, ker)  # 梯度运算
    # print('mor:', time.time() - s)
    # s = time.time()
    img_multiply = cv2.multiply(img_gradient, multiply_ratio)  # 图像增益
    img_close = cv2.morphologyEx(img_multiply, cv2.MORPH_OPEN, ker)  # close运算
    # print('open:', time.time() - s)

    # Otsu 滤波
    ot_thresh = cv2.threshold(img_close, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)[1]
    ot_thresh_new = ot_thresh[crop_h:img_h_r - crop_h, crop_w:img_w_r - crop_w]
    del ot_thresh

    # titles = ['before', 'after']
    # images = [img_close, ot_thresh]
    # for n in range(2):
    #     plt.subplot(1, 2, n + 1)
    #     plt.imshow(images[n])  # 行，列，位置，标只能从1开始，不能从0
    #     plt.title(titles[n])
    #     plt.xticks([]), plt.yticks([])
    # plt.show()

    # return ot_thresh_new, crop_h, crop_w, cv_im
    return ot_thresh_new, crop_h, crop_w


def det_cor_handle(cv_im, kong, lens_mul, img_size, r_s, r_a):
    global temp_kong_info
    h_im, w_im = cv_im.shape[:2]

    if kong == 25:  # 96 kong
        circle_9 = {}
        kong_num = 1
        for i in range(2):
            print('kong_num:', kong_num)
            crop_ratio, resize_ratio, param_r, min_r, max_r = img_handle(kong, lens_mul, img_size, kong_num)
            ot_thresh_new, crop_h, crop_w = morphology_img(cv_im, resize_ratio, crop_ratio, kong, lens_mul, kong_num)

            # review
            # ot_thresh_new, crop_h, crop_w, resize_im = morphology_img(cv_im, resize_ratio, crop_ratio, kong, lens_mul)
            try:
                circles_det = cv2.HoughCircles(ot_thresh_new, cv2.HOUGH_GRADIENT, 1, 100, param1=100, param2=param_r,
                                               minRadius=min_r,
                                               maxRadius=max_r)
                circle_sort = sorted(circles_det[0], key=lambda x_lb: x_lb[2], reverse=True)
                x7, y7, r7 = math.ceil(circle_sort[0][0]), math.ceil(circle_sort[0][1]), math.ceil(circle_sort[0][2])
                x_ori, y_ori = (x7 + crop_w) * resize_ratio, (y7 + crop_h) * resize_ratio

                r_pix = -2
                if kong == 23 and lens_mul == 4:
                    r_pix = 2

                r_ori = (r7 + r_pix) * resize_ratio
                circle_re = (x_ori, y_ori, r_ori)

                # if i == 0:
                #     cv_im_ = cv_im.copy()
                # else:
                #     cv_im_ = cv_im
                # cv2.circle(cv_im_, (x_ori, y_ori), r_ori, (0, 255, 0), 20)  # ok
                # plt.imshow(cv_im_)
                # plt.show()

            except:
                circle_re = []

            circle_9[i] = circle_re
            kong_num = 2
            del ot_thresh_new

        # print(circle_9)
        if len(circle_9) == 0:
            circle_re = []
        elif len(circle_9) == 1:
            circle_re = circle_9[0]
            circle_re = set(circle_re)
        else:
            h_im_c, w_im_c = h_im / 2, w_im / 2
            ciecle_x_0, ciecle_y_0 = circle_9[0][0], circle_9[0][1]
            ciecle_x_1, ciecle_y_1 = circle_9[1][0], circle_9[1][1]

            offset_0 = math.sqrt((ciecle_x_0 - w_im_c) ** 2 + (ciecle_y_0 - h_im_c) ** 2)
            offset_1 = math.sqrt((ciecle_x_1 - w_im_c) ** 2 + (ciecle_y_1 - h_im_c) ** 2)
            print('offset:', offset_0, offset_1)
            if offset_0 <= offset_1:
                print('offset_0')
                circle_re = circle_9[0]
            else:
                print('offset_1')
                circle_re = circle_9[1]
            circle_re = set(circle_re)


    if kong != 26:  #

        if r_s == 0:  # first kong det
            kong_num = 1
            circle_first = {}
            for i in range(2):
                print('first kong det:', kong_num)
                crop_ratio, resize_ratio, param_r, min_r, max_r = img_handle(kong, lens_mul, img_size, kong_num)
                ot_thresh_new, crop_h, crop_w = morphology_img(cv_im, resize_ratio, crop_ratio, kong,  lens_mul, kong_num)

                # review
                # ot_thresh_new, crop_h, crop_w, resize_im = morphology_img(cv_im, resize_ratio, crop_ratio, kong, lens_mul)
                try:
                    circles_det = cv2.HoughCircles(ot_thresh_new, cv2.HOUGH_GRADIENT, 1, 100, param1=100, param2=param_r,
                                                   minRadius=min_r,
                                                   maxRadius=max_r)
                    # print(circles_det)

                    circle_sort = sorted(circles_det[0], key=lambda x_lb: x_lb[2], reverse=True)
                    x7, y7, r7 = math.ceil(circle_sort[0][0]), math.ceil(circle_sort[0][1]), math.ceil(circle_sort[0][2])
                    # print(r7)
                    x_ori, y_ori = (x7 + crop_w) * resize_ratio, (y7 + crop_h) * resize_ratio

                    r_pix = -2
                    if kong == 23 and lens_mul == 4:
                        r_pix = 2

                    r_ori = (r7 + r_pix) * resize_ratio
                    # circle_re = (x_ori, y_ori, r_ori)
                    circle_re = [x_ori, y_ori, r_ori]

                    # cv2.circle(cv_im, (x_ori, y_ori), r_ori, (255, 0, 0), 20)  # ok
                    # plt.imshow(cv_im)
                    # plt.show()

                except:
                    circle_re = []

                circle_first[i] = circle_re
                kong_num = 2
                del ot_thresh_new

                # print(circle_9)
            if len(circle_first) == 0:
                circle_re = []
            elif len(circle_first) == 1:
                circle_re = circle_first[0]
                circle_re = set(circle_re)
            else:
                h_im_c, w_im_c = h_im / 2, w_im / 2
                ciecle_x_0, ciecle_y_0 = circle_first[0][0], circle_first[0][1]
                ciecle_x_1, ciecle_y_1 = circle_first[1][0], circle_first[1][1]

                offset_0 = math.sqrt((ciecle_x_0 - w_im_c) ** 2 + (ciecle_y_0 - h_im_c) ** 2)
                offset_1 = math.sqrt((ciecle_x_1 - w_im_c) ** 2 + (ciecle_y_1 - h_im_c) ** 2)
                print('offset:', offset_0, offset_1)
                if offset_0 <= offset_1:
                    print('offset_0')
                    circle_re = circle_first[0]
                else:
                    print('offset_1')
                    circle_re = circle_first[1]
                # circle_re = set(circle_re)

        else:  # not first kong
            kong_num = 1
            crop_ratio, resize_ratio, param_r, min_r, max_r = img_handle(kong, lens_mul, img_size, kong_num)
            ot_thresh_new, crop_h, crop_w = morphology_img(cv_im, resize_ratio, crop_ratio, kong, lens_mul, kong_num)

            # review
            # ot_thresh_new, crop_h, crop_w, resize_im = morphology_img(cv_im, resize_ratio, crop_ratio, kong, lens_mul)
            try:
                circles_det = cv2.HoughCircles(ot_thresh_new, cv2.HOUGH_GRADIENT, 1, 100, param1=100, param2=param_r,
                                               minRadius=min_r,
                                               maxRadius=max_r)
                # print(circles_det)

                circle_sort = sorted(circles_det[0], key=lambda x_lb: x_lb[2], reverse=True)
                x7, y7, r7 = math.ceil(circle_sort[0][0]), math.ceil(circle_sort[0][1]), math.ceil(circle_sort[0][2])
                # print(r7)
                x_ori, y_ori = (x7 + crop_w) * resize_ratio, (y7 + crop_h) * resize_ratio

                r_pix = -2
                if kong == 23 and lens_mul == 4:
                    r_pix = 2

                r_ori = (r7 + r_pix) * resize_ratio
                # circle_re = (x_ori, y_ori, r_ori)
                circle_re = [x_ori, y_ori, r_ori]

                # cv2.circle(cv_im, (x_ori, y_ori), r_ori, (255, 0, 0), 20)  # ok
                # plt.imshow(cv_im)
                # plt.show()

            except:
                circle_re = []
            del ot_thresh_new


        # print(len(circle_re))
        if len(circle_re) == 0:
            circle_re = []
        else:
            if temp_kong_info is not None:
                line = temp_kong_info.strip()
                kong_info = line.split(' ')[-1]
                kong_last = kong_info.split('_')   # str

                if r_s == 2 or r_s == 1:  # 可进行均值对比
                    if abs(circle_re[2] - r_a) >= 0.003 * r_a:
                        print('r:', circle_re[2])
                        print('prepare avg')
                        circle_re[0] = int(kong_last[0])
                        circle_re[1] = int(kong_last[1])
                        circle_re[2] = math.ceil(r_a)
                        # circle_re[2] = int(kong_last[2])
                        # print('after:', circle_re[2]/resize_ratio)
                    det_write = f' {circle_re[0]}_{circle_re[1]}_{circle_re[2]}'

                # elif r_s == 1:  # 有一个识别结果 往后追加半径
                #     det_write = f' {circle_re[0]}_{circle_re[1]}_{circle_re[2]}'
                else:
                    det_write = None

                # temp_kong_info = f'{temp_kong_info}{det_write}'
                temp_kong_info = temp_kong_info + det_write

            else:  # 之前没有识别结果
                det_write = f'{kong} {circle_re[0]}_{circle_re[1]}_{circle_re[2]}'
                temp_kong_info = det_write

            # print('txt:', temp_kong_info)
            # cv2.circle(cv_im, (circle_re[0], circle_re[1]), circle_re[2], (0, 255, 0), 20)
            # plt.imshow(cv_im)
            # plt.show()

            # circle_re = set(circle_re)
            # print('finalcircle_re:', circle_re)

        # else:
        #     h_im_c, w_im_c = h_im / 2, w_im / 2
        #     ciecle_x_0, ciecle_y_0 = circle_kong[0][0], circle_kong[0][1]
        #     ciecle_x_1, ciecle_y_1 = circle_kong[1][0], circle_kong[1][1]
        #
        #     offset_0 = math.sqrt((ciecle_x_0 - w_im_c) ** 2 + (ciecle_y_0 - h_im_c) ** 2)
        #     offset_1 = math.sqrt((ciecle_x_1 - w_im_c) ** 2 + (ciecle_y_1 - h_im_c) ** 2)
        #     print('offset:', offset_0, offset_1)
        #
        #     # 偏移比较
        #     if offset_0 <= offset_1:
        #         print('offset_0')
        #         circle_re = circle_kong[0]
        #     else:
        #         print('offset_1')
        #         circle_re = circle_kong[1]
        #
        #     # 均值比较
        #     file_kong = open('r.txt', 'a')
        #     if r_s == 2:  # 可进行均值对比
        #         if abs(circle_re[2] - r_a) >= 0.02 * r_a:
        #             circle_re[2] = math.ceil(r_a)
        #         det_write = f' {circle_re[2]}'
        #     elif r_s == 1:  # 有一个识别结果 往后追加半径
        #         det_write = f' {circle_re[2]}'
        #     else:  # 没有识别结果
        #         det_write = f'{kong} {circle_re[2]}'
        #     file_kong.write(det_write)
        # print(circle_re)

    else:
        kong_num = 1
        crop_ratio, resize_ratio, param_r, min_r, max_r = img_handle(kong, lens_mul, img_size, kong_num)
        h_im, w_im = cv_im.shape[:2]
        ot_thresh_new, crop_h, crop_w = morphology_img(cv_im, resize_ratio, crop_ratio, kong, lens_mul, kong_num)

        # review
        # ot_thresh_new, crop_h, crop_w, resize_im = morphology_img(cv_im, resize_ratio, crop_ratio, kong)
        # print(resize_im.shape[:2])
        # print('crop:', crop_h, crop_w)

        # ###384
        if kong == 26:
            try:
                contour = cv2.findContours(ot_thresh_new, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)[0]
                # cv_im = cv2.drawContours(cv_im, contour, -1, (0, 0, 255), 6)
                resize_h, resize_w = ot_thresh_new.shape[:2]

                rec_list = []
                for cnt in contour:
                    x_left, y_left, rect_w, rect_h = cv2.boundingRect(cnt)

                    min_rec = min(rect_w, rect_h)
                    if min_rec <= 50:
                        continue

                    area = rect_w * rect_h
                    rec_list.append((x_left, y_left, rect_w, rect_h, area))

                rec_list = sorted(rec_list, key=lambda x_lb: x_lb[4], reverse=True)
                x_left, y_left, rect_w, rect_h, area = rec_list[0]

                x_right, y_right = x_left + rect_w, y_left + rect_h
                rect_w = rect_w + crop_w
                rect_h = rect_h + crop_h

                if x_left == 0:
                    x1 = math.ceil(x_left)
                    y1 = math.ceil(y_left + crop_h)

                    y1 = math.ceil(y1 + 1.2 * crop_h)
                    x2, y2 = math.ceil(x1 + rect_w), math.ceil(y1 + rect_h)
                    x2, y2 = math.ceil(x2 - 0.5 * crop_w), math.ceil(y2 - 2.4 * crop_h)

                elif y_left == 0:
                    y1 = math.ceil(y_left)
                    x1 = math.ceil(x_left + crop_w)

                    x1 = math.ceil(x1 + 1.2 * crop_w)
                    x2, y2 = math.ceil(x1 + rect_w), math.ceil(y1 + rect_h)
                    x2, y2 = math.ceil(x2 - 2.4 * crop_w), math.ceil(y2 - 1.2 * crop_h)
                    # rect_h = rect_h + crop_h

                elif x_left == 0 and y_left == 0:
                    y1 = math.ceil(y_left)
                    x1 = math.ceil(x_left)
                    x2, y2 = math.ceil(x1 + rect_w), math.ceil(y1 + rect_h)
                    x2, y2 = math.ceil(x2 - 1.2 * crop_w), math.ceil(y2 - 1.2 * crop_h)

                elif x_right >= resize_w:
                    # print('hhhhhhhhhh')
                    x1, y1 = math.ceil(x_left + crop_w), math.ceil(y_left + crop_h)
                    x1, y1 = math.ceil(x1 + 1.2 * crop_w), math.ceil(y1 + 1.2 * crop_h)
                    x2, y2 = math.ceil(x1 + rect_w), math.ceil(y1 + rect_h)
                    x2, y2 = math.ceil(x2 - 1.2 * crop_w), math.ceil(y2 - 2.4 * crop_h)

                elif y_right >= resize_h:
                    x1, y1 = math.ceil(x_left + crop_w), math.ceil(y_left + crop_h)
                    x1, y1 = math.ceil(x1 + 1.2 * crop_w), math.ceil(y1 + 1.2 * crop_h)
                    x2, y2 = math.ceil(x1 + rect_w), math.ceil(y1 + rect_h)
                    x2, y2 = math.ceil(x2 - 2.4 * crop_w), math.ceil(y2 - 1.2 * crop_h)

                else:
                    x1, y1 = math.ceil(x_left + crop_w), math.ceil(y_left + crop_h)
                    x1, y1 = math.ceil(x1 + 1 * crop_w), math.ceil(y1 + 1 * crop_h)
                    x2, y2 = math.ceil(x1 + rect_w), math.ceil(y1 + rect_h)
                    x2, y2 = math.ceil(x2 - 3.2 * crop_w), math.ceil(y2 - 3.2 * crop_h)

                x1, y1, x2, y2 = x1 * resize_ratio, y1 * resize_ratio, x2 * resize_ratio, y2 * resize_ratio

                x1, y1 = max(0, x1), max(0, y1)
                x2, y2 = min(w_im, x2), min(h_im, y2)
                circle_re = (x1, y1, x2, y2)

                # cv2.rectangle(cv_im, (x1, y1), (x2, y2), (0, 255, 0), 2)
                # plt.imshow(cv_im)
                # plt.show()
            except Exception as e:
                circle_re = []

        else:
            circle_re = []
        del ot_thresh_new

    return circle_re


def handle_det_result(cv_im, kong, lens_mul, view_judge, img_size, r_s, r_a):
    circle_info = det_cor_handle(cv_im, kong, lens_mul, img_size, r_s, r_a)

    h_im, w_im = cv_im.shape[:2]
    mask_im = np.zeros((h_im, w_im), dtype=np.uint8)
    kong_status = True
    if circle_info is None or len(circle_info) == 0 or view_judge == 0:
        # circle_info = []
        kong_status = False
        mask_im = np.ones((h_im, w_im), dtype=np.uint8)

        # review
        # im_det = cv2.bitwise_and(cv_im, cv_im, mask=mask_im)

    elif kong == 26:
        # print('384----384----384')
        # rectangle
        x1, y1, x2, y2 = circle_info
        mask_im = cv2.rectangle(mask_im, (x1, y1), (x2, y2), 1, -1)  # 绘图

        # im_det = cv2.bitwise_and(cv_im, cv_im, mask=mask_im)

    else:  # circle
        x_ori, y_ori, r_ori = circle_info
        mask_im = cv2.circle(mask_im, (x_ori, y_ori), r_ori, 1, -1)
        mask_im = mask_im.astype(np.uint8)

        # im_det = cv2.bitwise_and(cv_im, cv_im, mask=mask_im)

    # plt.imshow(im_det)
    # plt.show()

    # return mask_im, im_det   # review
    return mask_im,kong_status
def extect_Contours(labelmap):
    img_h, img_w = labelmap.shape
    center_h, center_w = int(img_h / 2), int(img_w / 2)
    try:
        c = cv2.findContours(labelmap, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE, offset=(-1, -1))[0]
        log.info('原始检测孔数：' + str(len(c)))
        # print('原始检测孔数：', len(c))
        if len(c):
            center_con = []
            for c_n in range(len(c)):
                x, y, w, h = cv2.boundingRect(c[c_n])
                x_r, y_r = x + w, y + h
                # 在原图上画出预测的矩形
                # cv2.rectangle(label_map, (x, y), (x + w, y + h), (255, 255, 255), 30)

                # 判断中心点在矩形框内
                if x <= center_w <= x_r and y <= center_h <= y_r:
                    center_con.append(c[c_n])
            # print('处理后孔数：', len(center_con))
            label_map_new = np.zeros(labelmap.shape, dtype='uint8')
            if len(center_con) == 1:
                log.info('中心点区域处理后孔数：' + str(len(center_con)))
                cv2.drawContours(label_map_new, center_con, -1, 1, -1)  # 绘图
            else:  # 取最大的区域
                d = [max(center_con, key=cv2.contourArea)]
                cv2.drawContours(label_map_new, d, -1, 1, -1)  # 绘图
                log.info('最大区域处理后孔数：' + str(len(center_con)))
            labelmap=label_map_new
    except:
        log.info("轮廓未检测到孔")
    return labelmap
temp_cropkong_mask=None
temp_cropkong_info=None
temp_kong_info = None
kong_list = None
AIcrop_model=None
# view_judge   0: slide    1: kong
def crop_hole(cv_img, kong_num, lens, view_judge,max_size=512):
    global temp_cropkong_mask, temp_cropkong_info, temp_kong_info, kong_list,AIcrop_model
    start_time=time.time()
    if AIcrop_model is None:
        log.info("init AIcrop_model")
        from AI_crophole_infer import AI_crop_inferengine
        config = yaml.load(open("config.yaml", encoding='utf-8'), Loader=yaml.FullLoader)
        AIcrop_model = AI_crop_inferengine(config["kuang_predictor"][0]["engine"])
        AIcrop_model.max_shape=max_size
        log.info("start AIcrop_model infer")
        log.info(f"init AIcrop_model cost time {time.time()-start_time}")
    if int(view_judge)==0:
        log.info(f"view_judge {view_judge} no crop hole")
        h_im, w_im = cv_img.shape[:2]
        mask_im = np.ones((h_im, w_im), dtype=np.uint8)
        log.info(f"crop hole cost time {time.time()-start_time} s")
        return mask_im
    else:
        log.info(f"view_judge {view_judge} crop hole")
        try:
            AIcrop_model.max_shape = max_size
            mask_im=AIcrop_model.predict(cv_img)
            print(cv_img.shape,mask_im.shape)
            mask_im=extect_Contours(mask_im)
            ker_num = int(max(mask_im.shape) * 0.03)
            if ker_num % 2 == 0:
                ker_num = ker_num + 1
            #mask_im = cv2.erode(mask_im, (int(ker_num), int(ker_num)),iterations=30)
            mask_im = cv2.blur(mask_im, (int(ker_num), int(ker_num))) #变平滑
            log.info(f"crop hole cost time {time.time() - start_time} s")
            # AIcrop_model.release()
            # AIcrop_model=None
            return mask_im
        except Exception as e:
            log.error(f"AI crop fail {e}")
    # 调用函数 传入cv格式图片以及图片路径
    if lens == 4:
        # cv_img = cv2.imread(img_path)
        cv_img_h, cv_img_w = cv_img.shape[:2]
        cv_img_size = max(cv_img_h, cv_img_w)
    else:
        # cv_img = cv2.imread(img_path, cv2.IMREAD_REDUCED_COLOR_2)

        cv_img_h, cv_img_w = cv_img.shape[:2]
        cv_img = cv2.resize(cv_img, (round(cv_img_w/2), round(cv_img_h/2)))
        cv_img_h, cv_img_w = cv_img.shape[:2]
        cv_img_size = max(cv_img_h, cv_img_w)

    if temp_cropkong_info is None:
        temp_cropkong_info=f"{kong_num},{lens}"

    if kong_list is None:
         kong_list =[[kong_num, lens]]

    # txt
    print('kong_list', kong_list)
    if kong_num != kong_list[-1][0] or lens != kong_list[-1][1]:
        print('不是同一个耗材,重置kong_txt')
        temp_kong_info = None
        temp_cropkong_mask = None
        temp_cropkong_info = None
        kong_list.append([kong_num, lens])

    if temp_kong_info is None:  # 每一个实验前首次保存TXT
        print('no kong txt')
        r_status = 0
        r_avg = 0
    else:
        r_info = {}  # {'23': [255, 256], '25': [265, 289]}
        line = temp_kong_info.strip()
        element_kong = line.split(' ')[0]
        element_str = line.split(' ')[1:]
        element = [int(e.split('_')[2]) for e in element_str]
        r_info[element_kong] = element

        if str(kong_num) in r_info.keys():
            r_list_len = len(r_info[str(kong_num)])
            if r_list_len == 1:  # 之前只识别了一个孔板信息,第二个孔板无法进行均值化
                print('one kong txt')
                r_status = 1
                r_avg = np.average(r_info[str(kong_num)])
            elif r_list_len > 1:
                print('avg prepare txt')
                r_status = 2
                r_avg = np.average(r_info[str(kong_num)])
            else:  # 之前的孔板识别结果为原图
                print('no kong txt')
                r_status, r_avg = 0, 0
        else:
            print('first kong info txt')
            r_status, r_avg = 0, 0

    print('r_status, r_avg:', r_status, r_avg)
    mask, kong_status = handle_det_result(cv_img, kong_num, lens, view_judge, cv_img_size, r_status, r_avg)

    if not kong_status:  # 未识别到圆孔
        if temp_cropkong_mask is None:  # 第一张图没有识别到孔，返回原图，会出现孔外
            print('no kong')
            return mask
        else:  # 使用上一个扣到圆孔的结果
            print('use last')
            if f"{kong_num},{lens}" == temp_cropkong_info:  # 耗材孔径、倍镜与上一个抠到的孔相同
                mask = temp_cropkong_mask
            else:
                print("第一张图没有识别到孔，返回原图")
                mask = mask
    if kong_status:  # 识别到圆孔
        if temp_cropkong_mask is None:
            print("存temp_mask")
            temp_cropkong_mask=mask
    return mask


def cal_area_poly(data1, data2):
    """
    任意两个图形的相交面积的计算
    :param data1: 当前物体
    :param data2: 待比较的物体
    :return: 当前物体与待比较的物体的面积交集
    """

    poly1 = Polygon(data1).convex_hull  # Polygon：多边形对象
    poly2 = Polygon(data2).convex_hull

    if not poly1.intersects(poly2):
        inter_area = 0  # 如果两四边形不相交
    else:
        inter_area = poly1.intersection(poly2).area  # 相交面积
    return inter_area


def move_overlap(seg):
    move_index, small_list = [], []
    for pol_num in range(len(seg)):
        if pol_num == (len(seg) - 1):
            break

        x, y, w_rec, h_rec = cv2.boundingRect((np.array(seg[pol_num]).astype('float32')))
        if min(w_rec, h_rec) <= 50:  # 过滤小目标
            small_list.append(pol_num)
            continue
        if pol_num in small_list:  # 小目标不计算
            continue

        if pol_num in move_index:  # 重叠的多边形索引列表，不再重复计算
            continue

        data_1 = seg[pol_num]

        data1_area = cv2.contourArea(np.array([data_1]).astype('float32'))

        for pol_num2 in range(pol_num + 1, len(seg)):  # 从第一个多边形之后的开始遍历
            if pol_num2 in move_index:  # 重叠的多边形索引列表，不再重复计算
                continue

            if pol_num2 in move_index:  # 重叠的多边形索引列表，不再重复计算
                continue

            data_2 = seg[pol_num2]
            try:
                area = cal_area_poly(data_1, data_2)
                if area != 0:  # 相交
                    data2_area = cv2.contourArea(np.array([data_2]).astype('float32'))
                    min_area = min(data1_area, data2_area)  # 小的面积

                    if area >= 0.8 * min_area:  # 相交面积超过小多边形面积的80%，则认为重叠
                        if min_area == data1_area:  # 重叠多边形为第一个，退出遍历
                            move_index.append(pol_num)  # 重叠多边形的索引
                            break
                        else:
                            move_index.append(pol_num2)
                else:
                    continue
            except Exception as e:
                #log.error(f"move_overlap {e}",exc_info=True)
                continue

    # 删除异常值
    def move_error():
        move_index.extend(small_list)
        move_index2 = list(set(move_index))
        move_index2.sort(reverse=True)  # 索引降序
        for j in range(len(move_index2)):
            seg.pop(move_index2[j])
        return seg

    move_index2 = list(set(move_index))
    move_index2.sort(reverse=True)  # 索引降序
    return move_index2


def handle_img(cv_im):
    img_h, img_w = cv_im.shape[:2]

    arr = np.array(cv_im)
    img_sz = int(arr.nbytes / (1024 * 1024 * 16))
    max_shape = max(img_h, img_w)

    if img_sz >= 85 or max_shape >= 30000:
        resize_ratio = 10
    # elif 50 <= img_sz < 85 or max_shape >= 5000:
    #     resize_ratio = 10
    elif 20 <= img_sz < 85 or max_shape >= 3000:
        resize_ratio = 5
    else:
        resize_ratio = 1
    print('ratio:', resize_ratio)

    img_h_r, img_w_r = img_h // resize_ratio, img_w // resize_ratio
    handle_im = cv2.resize(cv_im, (img_w_r, img_h_r))

    # crop_h, crop_w = int(img_h_r * 0.1), int(img_w_r * 0.1)
    # print(crop_w, crop_h)

    return handle_im, img_h_r, img_w_r, resize_ratio


def morphology_img_jiaodi(cv_im):
    handle_im, img_h_r, img_w_r, resize_ratio = handle_img(cv_im)
    crop_h, crop_w = int(img_h_r * 0.1), int(img_w_r * 0.1)
    print(crop_w, crop_h)

    # 高斯模糊
    blur = cv2.GaussianBlur(handle_im, (9, 9), 0)
    gray = cv2.cvtColor(blur, cv2.COLOR_BGR2GRAY)

    ker = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (9, 9))  # 定义结构性元素  椭圆
    img_gradient = cv2.morphologyEx(gray, cv2.MORPH_GRADIENT, ker)  # 梯度运算
    img_multiply = cv2.multiply(img_gradient, 10)  # 图像增益
    img_close = cv2.morphologyEx(img_multiply, cv2.MORPH_CLOSE, ker)  # close运算

    # cv2.namedWindow('2', 0)
    # cv2.resizeWindow('2', 1000, 1000)
    # cv2.imshow("2", imgMultiply)
    # cv2.waitKey(0)

    # Otsu 滤波
    ot_thresh = cv2.threshold(img_close, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)[1]

    ot_thresh_new = ot_thresh[crop_h:img_h_r - crop_h, crop_w:img_w_r - crop_w]
    try:
        contour = cv2.findContours(ot_thresh_new, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)[0]
    except Exception as e:
        contour = []

    # ima = cv2.drawContours(handle_im[crop_h:img_h_r - crop_h, crop_w:img_w_r - crop_w], contour, -1, (0, 0, 255), 6)
    # plt.imshow(ima)
    # plt.show()

    return contour, resize_ratio, img_h_r, img_w_r


def handle_cnt(cv_im):
    contour, resize_ratio, img_h_r, img_w_r = morphology_img_jiaodi(cv_im)
    img_h, img_w = cv_im.shape[:2]
    crop_h, crop_w = int(img_h_r * 0.1), int(img_w_r * 0.1)
    r_thresh = max(img_h_r, img_w_r) * 0.1  # filter too small circle

    circle_info = []

    try:
        for cnt in contour:
            (x_center, y_center), radius = cv2.minEnclosingCircle(cnt)
            if radius <= r_thresh:
                continue

            x_left, y_left, rect_w, rect_h = cv2.boundingRect(cnt)
            max_rect, min_rect = max(rect_w, rect_h), min(rect_w, rect_h)

            if max_rect / min_rect > 2:  # filter error w/h
                continue

            x_center_ori, y_center_ori = (x_center + crop_w) * 5, (y_center + crop_h) * 5
            radius_ori = radius * 5 + r_thresh
            x_center_ori, y_center_ori = int(x_center_ori), int(y_center_ori)
            radius_ori = int(radius_ori)

            x_left_add, y_left_add = x_center_ori - radius_ori, y_center_ori - radius_ori
            x_right_add, y_right_add = x_center_ori + radius_ori, y_center_ori + radius_ori

            x_left_add, y_left_add = max(0, x_left_add), max(0, y_left_add)
            x_right_add, y_right_add = min(img_w, x_right_add), min(img_h, y_right_add)

            # bbox_w, bbox_h = x_right_add - x_left_add, y_right_add - y_left_add

            # im2 = cv_im
            # im_rec = cv2.rectangle(im2, (x_left_add, y_left_add), (x_right_add, y_right_add), (255, 0, 0), 5)

            # center_ori = (x_center_ori, y_center_ori)
            # im_circle = cv2.circle(cv_im, center_ori, radius_ori, (0, 255, 0), 25)
            # plt.imshow(im_circle)
            # plt.show()

            # print((x_left_add, y_left_add), (x_right_add, y_right_add))
            circle_info.append([x_left_add, y_left_add, x_right_add, y_right_add])
            # circle_info.append([x_left_add, y_left_add, bbox_w, bbox_h])
    except Exception as e:
        circle_info = [[0, 0, img_w, img_h]]

    return circle_info, img_h, img_w


def handle_jiaodi(cv_im):
    circle_info, img_h, img_w = handle_cnt(cv_im)

    im_det = cv_im
    if circle_info is None or len(circle_info) == 0:
        circle_info = [[0, 0, img_w, img_h]]
        # circle_info = []

    elif len(circle_info) == 1:
        # print('00000000000')
        x_l, y_l = circle_info[0][0], circle_info[0][1]
        x_r, y_r = circle_info[0][2], circle_info[0][3]
        x_w, y_h = x_r - x_l, y_r - y_l

        # x_w, y_h = circle_info[0][2], circle_info[0][3]

        # x_r, y_r = x_l + x_w, y_l + y_h
        max_det_rec = min(x_w, y_h)
        max_img_size = max(img_w, img_h)
        # print(max_det_rec, max_img_size)

        if max_det_rec <= max_img_size * 0.3: #原始0.5用来过滤小误识别胶底
            # circle_info = []
            circle_info = [[0, 0, img_w, img_h]]
            # im_det = cv_im
        else:
            # im_det = cv2.rectangle(cv_im, (x_l, y_l), (x_r, y_r), (0, 255, 0), 5)
            circle_info = circle_info
    elif len(circle_info) <= 3:
        # print('33333')
        # print(circle_info)
        max_rec = sorted(circle_info, key=lambda x_lb: x_lb[2], reverse=True)[0]  # w
        min_rec = sorted(circle_info, key=lambda x_lb: x_lb[2], reverse=True)[-1]
        max_rec_h = sorted(circle_info, key=lambda x_lb: x_lb[3], reverse=True)[0]  # w
        min_rec_h = sorted(circle_info, key=lambda x_lb: x_lb[3], reverse=True)[-1]
        max_rec_w, min_rec_w = max_rec[2], min_rec[2]
        max_rec_h2, min_rec_h2 = max_rec_h[3], min_rec_h[3]
        if max_rec_w / min_rec_w >= 1.5:
            circle_info = [max_rec]
            # im_det = cv2.rectangle(cv_im, (max_rec[0], max_rec[1]), (max_rec[0], max_rec[1]), (255, 0, 0), 5)
        if max_rec_h2 / min_rec_h2 >= 1.5:
            # print('22222')
            print(max_rec_h)
            circle_info = [max_rec_h]
        #     im_det = cv2.rectangle(cv_im, (max_rec_h[0], max_rec_h[1]), (max_rec_h[2], max_rec_h[3]),
        #                            (255, 0, 0), 5)
        #
        # plt.imshow(im_det)
        # plt.show()
    elif len(circle_info) >= 6:  # lots obj
        # print('11111111111111')
        # im_det = cv_im
        circle_info = [[0, 0, img_w, img_h]]
        # circle_info = []
    else:
        circle_info = circle_info
        # for i in circle_info:
        #     x_l, y_l = i[0], i[1]
        #     # x_r, y_r = i[2], i[3]
        #     x_r, y_r = x_l + i[2], y_l + i[3]
        #     im_det = cv2.rectangle(cv_im, (x_l, y_l), (x_r, y_r), (255, 0, 0), 5)
        # print('222222')

    # return circle_info, im_det
    return circle_info


def crop_muti_image(cv_im):

    return [[0,0,cv_im.shape[1],cv_im.shape[0]]]
    #return handle_jiaodi(cv_im)

from tqdm import tqdm
import imutils.paths

if __name__ == '__main__':
    # im0=cv2.imread(r"H:\孔外识别\孔外识别\6-temp\B1_01_BF_merge_src")
    # dir = r'/home/<USER>/下载/archive/plate3/n'
    # for img_path in tqdm(imutils.paths.list_images(dir)):
    #     im0 = cv2.imdecode(np.fromfile(img_path, dtype=np.uint8), cv2.IMREAD_COLOR)
    #     mask = crop_hole(im0, 23, 4, 1)
    #     plt.imshow(mask)
    #     plt.show()


    # temp_cropkong_mask = None
    # temp_cropkong_info = None
    dir = r'D:\detect\paddleGAN\datasets\新建文件夹'
    for img_path in tqdm(imutils.paths.list_images(dir)):
        # if "A1_01_BF_cut(1).jpg" not in img_path:
        #     continue
        print(img_path)
        im0 = cv2.imdecode(np.fromfile(img_path, dtype=np.uint8), cv2.IMREAD_COLOR)
        # plt.imshow(im0)
        # plt.show()
        mask = crop_hole(im0, 25, 4,1)
        crop_img = cv2.bitwise_and(im0, im0, mask=mask)
        cv2.imencode('.jpg', crop_img)[1].tofile(os.path.join(dir, img_path.split(os.sep)[-1][:-4] + "_crop.jpg"))
        #print(os.path.join(dir,img_path.split(os.sep)[-1][:-4]+"_crop.jpg"))
        #cv2.imwrite(os.path.join(dir,img_path.split(os.sep)[-1][:-4]+"_crop1.jpg"),crop_img)
        #plt.imshow(cv2.cvtColor(crop_img, cv2.COLOR_BGR2RGB))
        #plt.show()
    # print(crop_box)
    # im0=cv2.rectangle(im0, (crop_box[0], crop_box[1]), (crop_box[2], crop_box[3]), (0, 255, 0), 5)
    # plt.imshow(im0)
    # plt.show()
    # plt.imshow(mask)
    # plt.show()
    # crop_img = cv2.bitwise_and(im0, im0, mask=mask)
    # cv2.imencode('.jpg', crop_img)[1].tofile(r"C:\Users\<USER>\Downloads\6-temp_crop.jpg")