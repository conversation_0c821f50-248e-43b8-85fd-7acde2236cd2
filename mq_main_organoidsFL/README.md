# organoidsFL
## AI类器官荧光死活分析算法
### 版本:1.0.0.0
### 调用接口:
|参数名称|参数说明|参数类型|是否必填|备注|
|----|----|----|----|----|
|exp_type|实验类型|String|是|取值为“organoidsFL”|
|BF_input_path|输入明场通道图片地址|String|是|绝对路径|
|FL1_input_path|输入 FL1 通道图片地址|String|是|绝对路径|
|FL2_input_path|输入 FL2 通道图片地址|String|是|绝对路径|
|FL3_input_path|输入 FL3 通道图片地址|String|是|绝对路径|
|BF_labelme_path|输入 BF 的 labelme 文件地址|String|是|绝对路径|
|FL1_labelme_path|输入 FL1 的 labelme 文件地址|String|是|绝对路径|
|FL2_labelme_path|输入 FL2 的 labelme 文件地址|String|是|绝对路径|
|FL3_labelme_path|输入 FL3 的 labelme 文件地址|String|是|绝对路径|
|BF_save_path|输出明场图片地址|String|是|绝对路径|
|FL1_save_path|输出 FL1 图片地址|String|是|绝对路径|
|FL2_save_path|输出 FL2 图片地址|String|是|绝对路径|
|FL3_save_path|输出 FL3 图片地址|String|是|绝对路径|
|merge_save_path|输出融合图片地址|String|是|绝对路径|
|data_save_path|统计数据输出地址|String|是|绝对路径,csv 格式|
|fcs_save_path|类流式分析数据输出地址|String|是|绝对路径,csv 格式|
|BF_conf_thrd|置信度|Float|是|范围 0~1 之间,保留 2 位小数,默认值 0.5|
|BF_iou_thrd|交并比|Float|是|范围 0~1 之间,保留 2 位小数,默认值 0.1|
|FL1_gray_tred|FL1 荧光分割强度|Int|是|用于过滤虚化的边缘,取值范围 0~255,默认值 0|
|FL2_gray_tred|FL2 荧光分割强度|Int|是|用于过滤虚化的边缘,取值范围 0~255,默认值 0|
|FL3_gray_tred|FL3 荧光分割强度|Int|是|用于过滤虚化的边缘,取值范围 0~255,默认值 0|
|FL1_sharpen_tred|FL1 荧光锐利度|Float|是|用于过滤 FL1 荧光场中的死细胞,范围 0.25~0.3,默认 0.28|
|FL2_sharpen_tred|FL2 荧光锐利度|Float|是|用于过滤 FL2 荧光场中的活细胞,范围 0.25~0.3,默认 0.28|
|FL3_sharpen_tred|FL3 荧光锐利度|Float|是|用于过滤 FL3 荧光场中的活细胞,范围 0.25~0.3,默认 0.28|
|cut_info|抠图参数|String|否|格式:孔类型,倍率,是否抠孔,中间用英文逗号隔开,其中最后一位<br>0:为不抠孔<br>1:为抠孔<br>例子:”25,4,1”,或”21,10,0”|
|tid|任务 id|String|是|通过 uuid3 生成的唯一字符串|