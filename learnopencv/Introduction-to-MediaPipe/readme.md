# Introduction to MediaPipe

**This repository contains code for [Introduction to Mediapipe](https://learnopencv.com/introduction-to-mediapipe/) blogpost**.

[<img src="https://learnopencv.com/wp-content/uploads/2022/07/download-button-e1657285155454.png" alt="download" width="200">](https://www.dropbox.com/sh/m71wktssm0vtenf/AACyM9vndSfV4nTuUAiW6j5-a?dl=1)

You can run the example Notebook on [Google Colab](https://colab.research.google.com/github/spmallick/learnopencv/blob/master/Introduction-to-MediaPipe/MediaPipe-sample-solutions.ipynb)

## Dependencies
Install required packages using the following command.

```
pip install -r requirements.txt
```

# AI Courses by OpenCV

Want to become an expert in AI? [AI Courses by OpenCV](https://opencv.org/courses/) is a great place to start. 

<a href="https://opencv.org/courses/" target="_blank">
<p align="center"> 
<img src="https://www.learnopencv.com/wp-content/uploads/2020/04/AI-Courses-By-OpenCV-Github.png">
</p>
</a>
