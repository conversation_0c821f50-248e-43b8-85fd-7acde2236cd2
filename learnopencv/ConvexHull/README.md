**Usage:**

**Python**

`python3 example.py <image_path>`

It takes sample.jpg (default) - if no argument provided.

**C++**

```g++ example.cpp `pkg-config opencv --cflags --libs` -o example```

OR directly run the program using `./example <image_path>`


# AI Courses by OpenCV

Want to become an expert in AI? [AI Courses by OpenCV](https://opencv.org/courses/) is a great place to start. 

<a href="https://opencv.org/courses/">
<p align="center"> 
<img src="https://www.learnopencv.com/wp-content/uploads/2020/04/AI-Courses-By-OpenCV-Github.png">
</p>
</a>
