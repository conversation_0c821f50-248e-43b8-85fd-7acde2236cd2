import os

import PIL
import cv2
import numpy as np
os.environ["OPENCV_IO_MAX_IMAGE_PIXELS"] = str(pow(2, 50))

x20img_crop=cv2.imread("20X_crop.jpg")
x20img_countours=cv2.findContours(cv2.cvtColor(x20img_crop, cv2.COLOR_BGR2GRAY), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)[0]
x20img_countours=sorted(x20img_countours, key=cv2.contourArea, reverse=True)[0]
x20_box=cv2.boundingRect(x20img_countours)
print("x20_box",x20_box)
x,y,w,h=x20_box
x20img=x20img_crop[y:y+h, x:x+w]
cv2.imwrite("x20img_rect.jpg", x20img)
x20_shape=x20img.shape[:2][::-1]
x10img_crop=cv2.imread("10X_crop.jpg")
x10img_countours=cv2.findContours(cv2.cvtColor(x10img_crop, cv2.COLOR_BGR2GRAY), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)[0]
x10img_countours=sorted(x10img_countours, key=cv2.contourArea, reverse=True)[0]
x10_box=cv2.boundingRect(x10img_countours)
print("x10_box",x10_box)
x,y,w,h=x10_box
x10img=x10img_crop[y:y+h, x:x+w]
x10img_resize=cv2.resize(x10img,x20_shape)
cv2.imwrite("x10img_rect.jpg", x10img)
x4imgg_crop=cv2.imread("4X_crop.jpg")
x4img_countours=cv2.findContours(cv2.cvtColor(x4imgg_crop, cv2.COLOR_BGR2GRAY), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)[0]
x4img_countours=sorted(x4img_countours, key=cv2.contourArea, reverse=True)[0]
x4_box=cv2.boundingRect(x4img_countours)
print("x4_box",x4_box)
x,y,w,h=x4_box
x4img=x4imgg_crop[y:y+h, x:x+w]
x4img_resize=cv2.resize(x4img,x20_shape)
cv2.imwrite("x4img_rect.jpg", x4img)
print(x20img.shape,x10img_resize.shape,x4img_resize.shape)


