import os
os.environ["OPENCV_IO_MAX_IMAGE_PIXELS"] = str(pow(2, 50))
from tkinter import filedialog
import pycuda.driver as cuda
import tensorrt as trt
import cv2
import numpy as np
from imutils.paths import list_images
from loguru import logger as log
log.add("pix2pix_dynamic_axes_tensorrt.log", rotation="10 MB")
class pix2pixTRT():
    def __init__(self, model_file_path,max_shape=4096):
        super().__init__()
        if not os.path.exists(model_file_path):
            log.error("model engine file is not exists in {}".format(model_file_path))
        log.info("loading model from {}".format(model_file_path))
        self.model_file_path = model_file_path
        self.batch_size = 1
        cuda.init()
        self.device = cuda.Device(0)
        log.info("Device {}: {}".format(0, self.device.name()))
        log.info("Compute Capability: {}.{}".format(*self.device.compute_capability()))
        log.info("Total Memory: {} megabytes".format(self.device.total_memory() // (1024 ** 2)))
        log.info(f"GPU-{0} start loading model from {self.model_file_path}")
        self.cuda_driver_context = self.device.retain_primary_context()
        self.logger = trt.Logger(trt.Logger.ERROR)
        with open(self.model_file_path, "rb") as f, trt.Runtime(self.logger) as runtime:
            self.engine = runtime.deserialize_cuda_engine(f.read())
        try:
            assert self.engine
            self.context = self.engine.create_execution_context()
            assert self.context
        except Exception as e:
            with open(model_file_path, "rb") as f, trt.Runtime(self.logger) as runtime:
                self.engine = runtime.deserialize_cuda_engine(f.read())
            self.context = self.engine.create_execution_context()
            log.error(f"reload engine {e}")
        # get engine max input/output shape
        print(self.context.get_binding_shape(0))
        if self.context.get_binding_shape(0)[-1] == -1:
            self.dynamic_mode = True
            print("loading model from {}".format(model_file_path), "dynamic_mode", self.dynamic_mode)
        else:
            self.dynamic_mode = False
            self.fix_shape = self.context.get_binding_shape(0)[2:]
            # print("fix_shape:",self.fix_shape)
            self.allocate_buffers(self.context.get_binding_shape(0))
            print("loading model from {}".format(model_file_path), "dynamic_mode", self.dynamic_mode, "fix_shape:",
                  self.context.get_binding_shape(0))
        self.max_shape=max_shape
        self.to_bgr = True
        self.mean=np.array([0.5, 0.5, 0.5])
        self.std=np.array([0.5, 0.5, 0.5])
    def allocate_buffers(self, input_shape=None):
        self.inputs = []
        self.outputs = []
        self.allocations = []
        self.batch_size = input_shape[0]
        self.cuda_driver_context.push()
        for i in range(self.engine.num_bindings):
            name = self.engine.get_binding_name(i)
            dtype = self.engine.get_binding_dtype(i)
            if self.engine.binding_is_input(i):
                if -1 in tuple(self.engine.get_binding_shape(i)):  # dynamic
                    self.context.set_binding_shape(i, tuple(input_shape))
            shape = tuple(self.context.get_binding_shape(i))
            size = np.dtype(trt.nptype(dtype)).itemsize
            for s in shape:
                size = size * s
            allocation = cuda.mem_alloc(size)
            binding = {
                'index': i,
                'name': name,
                'dtype': np.dtype(trt.nptype(dtype)),
                'shape': list(shape),
                'allocation': allocation,
            }
            self.allocations.append(allocation)
            if self.engine.binding_is_input(i):
                self.inputs.append(binding)
            else:
                self.outputs.append(binding)
        self.cuda_driver_context.pop()
        # print("dynamic_mode",self.dynamic_mode)
        # print(self.inputs[0]['shape'], self.inputs[0]['dtype'])
        # print(self.outputs[0]['shape'], self.outputs[0]['dtype'])
        assert self.batch_size > 0
        assert len(self.inputs) > 0
        assert len(self.outputs) > 0
        assert len(self.allocations) > 0
    def resize(self,im, target_size=608, interp=cv2.INTER_LINEAR):
        if isinstance(target_size, list) or isinstance(target_size, tuple):
            w = target_size[0]
            h = target_size[1]
        else:
            w = target_size
            h = target_size
        im = cv2.resize(im, (w, h), interpolation=interp)
        return im
    def resize_padding(self,im, max_side_len=2400):
        '''
        resize image to a size multiple of 32 which is required by the network
        :param im: the resized image
        :param max_side_len: limit of max image size to avoid out of memory in gpu
        :return: the resized image and the resize ratio
        '''
        h, w, _ = im.shape

        resize_w = w
        resize_h = h

        # limit the max side
        if max(resize_h, resize_w) > max_side_len:
            ratio = float(
                max_side_len) / resize_h if resize_h > resize_w else float(
                max_side_len) / resize_w
        else:
            ratio = 1.
        resize_h = int(resize_h * ratio)
        resize_w = int(resize_w * ratio)

        resize_h = resize_h if resize_h % 32 == 0 else (resize_h // 32 - 1) * 32
        resize_w = resize_w if resize_w % 32 == 0 else (resize_w // 32 - 1) * 32
        resize_h = max(32, resize_h)
        resize_w = max(32, resize_w)
        im = cv2.resize(im, (int(resize_w), int(resize_h)))
        # im = cv2.resize(im, (512, 512))
        ratio_h = resize_h / float(h)
        ratio_w = resize_w / float(w)
        _ratio = np.array([ratio_h, ratio_w]).reshape(-1, 2)
        return im, _ratio
    def resize_long(self,im, long_size=224, interpolation=cv2.INTER_LINEAR):
        value = max(im.shape[0], im.shape[1])
        scale = float(long_size) / float(value)
        resized_width = int(round(im.shape[1] * scale))
        resized_height = int(round(im.shape[0] * scale))
        im = cv2.resize(
            im, (resized_width, resized_height), interpolation=interpolation)
        return im
    def normalize(self,im, mean, std, min_value=[0, 0, 0], max_value=[255, 255, 255]):
        # Rescaling (min-max normalization)
        range_value = [max_value[i] - min_value[i] for i in range(len(max_value))]
        im = (im - min_value) / range_value

        # Standardization (Z-score Normalization)
        im -= mean
        im /= std
        return im.astype('float32')

    def permute(self,im, to_bgr=False):
        im = np.swapaxes(im, 1, 2)
        im = np.swapaxes(im, 1, 0)
        if to_bgr:
            im = im[[2, 1, 0], :, :]
        return im
    def preprocess(self, image):
        if isinstance(image, str):
            image=cv2.imdecode(np.fromfile(image, dtype=np.uint8), -1)
        elif isinstance(image, np.ndarray):
            image=image.copy()
        res = dict()
        print("image.shape", image.shape)
        im_info = [('resize', image.shape[:2])]
        height,width,_=image.shape
        new_height = height // 512
        new_width = width // 512
        if new_height == 0 or new_width == 0:
            image = cv2.resize(image, (512, 512))
        else:
            if new_width == 0:
                new_width = 1
            if new_height == 0:
                new_height = 1
            image = cv2.resize(image, (new_width * 512, new_height * 512))
        im = self.normalize(image, self.mean, self.std)
        im = self.permute(im, to_bgr=self.to_bgr)
        # print(self.dynamic_mode, im_info)
        # print("im.shape",im.shape)
        im = np.expand_dims(im, axis=0).copy()
        res['image'] = im
        res['im_info'] = im_info
        return res

    def generation(self, img_file):
        # Create builder
        preprocessed_inputs = self.preprocess(img_file)
        print("preprocessed_inputs.shape",preprocessed_inputs["image"].shape)
        self.allocate_buffers(preprocessed_inputs["image"].shape)
        cuda.memcpy_htod(self.inputs[0]['allocation'], np.ascontiguousarray(preprocessed_inputs['image']))
        self.context.execute_v2(self.allocations)
        output = np.zeros(self.outputs[0]['shape'], dtype=self.outputs[0]['dtype'])
        cuda.memcpy_dtoh(output, self.outputs[0]['allocation'])
        outs = np.clip(((output[0]*0.5+0.5) * 255), 0, 255).astype('uint8') #重要
        outs = outs.transpose(1, 2, 0).astype('uint8')
        outs = cv2.cvtColor(outs, cv2.COLOR_RGB2BGR)
        im_info = preprocessed_inputs['im_info']
        for info in im_info[::-1]:
            if info[0] == 'resize':
                w, h = info[1][1], info[1][0]
                outs = cv2.resize(outs, (w, h), cv2.INTER_NEAREST)
                # score_map = cv2.resize(score_map, (w, h), cv2.INTER_LINEAR)
            elif info[0] == 'padding':
                w, h = info[1][1], info[1][0]
                outs = outs[0:h, 0:w]
        return outs
    def generation_batch(self,
                             img_file,
                             tile_size=None,
                             pad_size=None):
        """有重叠的大图切小图预测。
        Args:
            img_file(str|np.ndarray): 预测图像路径，或者是解码后的排列格式为（H, W, C）且类型为float32且为BGR格式的数组。
            tile_size(list|tuple): 滑动窗口的大小，该区域内用于拼接预测结果，格式为（W，H）。默认值为[512, 512]。
            pad_size(list|tuple): 滑动窗口向四周扩展的大小，扩展区域内不用于拼接预测结果，格式为（W，H）。默认值为[64，64]。
            batch_size(int)：对窗口进行批量预测时的批量大小。默认值为32
            transforms(paddlex.cv.transforms): 数据预处理操作。


        Returns:
            dict: 包含关键字'label_map'和'score_map', 'label_map'存储预测结果灰度图，
                像素值表示对应的类别，'score_map'存储各类别的概率，shape=(h, w, num_classes)
        """

        if tile_size is None:
            tile_size = [2048, 2048]
        if pad_size is None:
            pad_size = [16, 16]
        if isinstance(img_file, str):
            image= cv2.imdecode(np.fromfile(img_file, dtype=np.uint8), cv2.IMREAD_COLOR)
            log.info(f"image_file {img_file}")
        elif isinstance(img_file, np.ndarray):
            image = img_file.copy()
        else:
            raise Exception("im_file must be str/ndarray")
        log.info(f"image_file.shape {image.shape}")
        image = cv2.resize(image, None, fx=5, fy=5, interpolation=cv2.INTER_CUBIC)
        height, width, channel = image.shape
        image_tile_list = list()

        # Padding along the left and right sides
        if pad_size[0] > 0:
            left_pad = cv2.flip(image[0:height, 0:pad_size[0], :], 1)
            right_pad = cv2.flip(image[0:height, -pad_size[0]:width, :], 1)
            padding_image = cv2.hconcat([left_pad, image])
            padding_image = cv2.hconcat([padding_image, right_pad])
        else:
            import copy
            padding_image = copy.deepcopy(image)

        # Padding along the upper and lower sides
        padding_height, padding_width, channel = padding_image.shape
        if pad_size[1] > 0:
            upper_pad = cv2.flip(
                padding_image[0:pad_size[1], 0:padding_width, :], 0)
            lower_pad = cv2.flip(
                padding_image[-pad_size[1]:padding_height, 0:padding_width, :],
                0)
            padding_image = cv2.vconcat([upper_pad, padding_image])
            padding_image = cv2.vconcat([padding_image, lower_pad])

        # crop the padding image into tile pieces
        padding_height, padding_width, channel = padding_image.shape

        for h_id in range(0, height // tile_size[1] + 1):
            for w_id in range(0, width // tile_size[0] + 1):
                left = w_id * tile_size[0]
                upper = h_id * tile_size[1]
                right = min(left + tile_size[0] + pad_size[0] * 2,
                            padding_width)
                lower = min(upper + tile_size[1] + pad_size[1] * 2,
                            padding_height)
                image_tile = padding_image[upper:lower, left:right, :]
                image_tile_list.append(image_tile)

        # predict
        out_img = np.zeros((height, width,channel), dtype=np.uint8)
        # score_map = np.zeros(
        #     (height, width, self.num_classes), dtype=np.float32)
        num_tiles = len(image_tile_list)
        print("num_tiles:", num_tiles)
        for i in range(0, num_tiles):
            res = self.generation(image_tile_list[i])
            cv2.imwrite("result/test_"+str(i)+".jpg",res)
            h_id = i // (width // tile_size[0] + 1)
            w_id = i % (width // tile_size[0] + 1)
            left = w_id * tile_size[0]
            upper = h_id * tile_size[1]
            right = min((w_id + 1) * tile_size[0], width)
            lower = min((h_id + 1) * tile_size[1], height)
            tile_img_map = res
            #tile_score_map = res["score_map"]
            tile_upper = pad_size[1]
            tile_lower = tile_img_map.shape[0] - pad_size[1]
            tile_left = pad_size[0]
            tile_right = tile_img_map.shape[1] - pad_size[0]
            out_img[upper:lower, left:right] = \
                tile_img_map[tile_upper:tile_lower, tile_left:tile_right]
        #out_img = cv2.resize(out_img, None, fx=0.2, fy=0.2, interpolation=cv2.INTER_CUBIC)
        return out_img


if __name__ == '__main__':
    root_path = filedialog.askdirectory(title="请选择要处理的文件夹路径")
    result_path = filedialog.askdirectory(title="请选择结果保存路径")
    try:
        gan_model=pix2pixTRT("SR_facedes_pix2pixB2A_e5000_ld512_dynamic_aix.engine")
        for img_path in list_images(root_path):
            if "_HR.jpg" in img_path:
                continue
            print("处理文件：",img_path)
            fack_result = gan_model.generation_batch(img_path)
            cv2.imencode(".jpg", fack_result)[1].tofile(os.path.join(result_path, img_path.split(os.sep)[-1][:-4]+"_HR.jpg"))
    except Exception as e:
        print(e)
        log.error(e)
    os.system("pause")