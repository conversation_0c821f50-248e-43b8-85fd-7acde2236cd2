#pragma once

// @generated by aten/src/ATen/gen.py from TypeDerived.h

#include <c10/core/TensorOptions.h>
#include <c10/core/Scalar.h>
#include <c10/core/QScheme.h>
#include <c10/core/MemoryFormat.h>
#include <c10/util/ArrayRef.h>
#include <c10/util/intrusive_ptr.h>
#include <torch/csrc/WindowsTorchApiMacro.h>
#include <ATen/Dimname.h>



namespace c10 {
struct Storage;
}

namespace at {

class Tensor;
using TensorList = ArrayRef<Tensor>;

class Context;
struct Generator;

struct Quantizer;
// This is temporary typedef to enable Quantizer in aten native function API
// we'll remove them when we are actually exposing Quantizer class
// to frontend
using ConstQuantizerPtr = const c10::intrusive_ptr<Quantizer>&;

namespace MkldnnCPUType {
  Tensor add_Tensor(const Tensor & self, const Tensor & other, Scalar alpha);
  Tensor & add__Tensor(Tensor & self, const Tensor & other, Scalar alpha);
  Tensor & add_out_out(Tensor & out, const Tensor & self, const Tensor & other, Scalar alpha);
  Tensor empty_memory_format(IntArrayRef size, const TensorOptions & options, c10::optional<MemoryFormat> memory_format);
  Tensor mkldnn_linear(const Tensor & input, const Tensor & weight, const Tensor & bias);
  Tensor mkldnn_max_pool2d(const Tensor & self, IntArrayRef kernel_size, IntArrayRef stride, IntArrayRef padding, IntArrayRef dilation, bool ceil_mode);
  Tensor mkldnn_max_pool3d(const Tensor & self, IntArrayRef kernel_size, IntArrayRef stride, IntArrayRef padding, IntArrayRef dilation, bool ceil_mode);
  Tensor mul_Tensor(const Tensor & self, const Tensor & other);
  Tensor & mul__Tensor(Tensor & self, const Tensor & other);
  Tensor & mul_out_out(Tensor & out, const Tensor & self, const Tensor & other);
  std::tuple<Tensor,Tensor,Tensor> native_batch_norm(const Tensor & input, const Tensor & weight, const Tensor & bias, const Tensor & running_mean, const Tensor & running_var, bool training, double momentum, double eps);
  Tensor _mkldnn_reshape(const Tensor & self, IntArrayRef shape);
  Tensor relu(const Tensor & self);
  Tensor & relu_(Tensor & self);
  Tensor sigmoid(const Tensor & self);
  Tensor & sigmoid_(Tensor & self);
  Tensor _softmax(const Tensor & self, int64_t dim, bool half_to_float);
  Tensor _mkldnn_transpose(const Tensor & self, int64_t dim0, int64_t dim1);
  Tensor & _mkldnn_transpose_(Tensor & self, int64_t dim0, int64_t dim1);
  Tensor clone(const Tensor & self, c10::optional<MemoryFormat> memory_format);
  Tensor & zero_(Tensor & self);
  Tensor to_dense(const Tensor & self);
  Tensor mkldnn_reorder_conv2d_weight(const Tensor & self, IntArrayRef padding, IntArrayRef stride, IntArrayRef dilation, int64_t groups);
  Tensor mkldnn_reorder_conv3d_weight(const Tensor & self, IntArrayRef padding, IntArrayRef stride, IntArrayRef dilation, int64_t groups);
  Tensor view(const Tensor & self, IntArrayRef size);
  Tensor & adaptive_avg_pool2d_out_out(Tensor & out, const Tensor & self, IntArrayRef output_size);
  Tensor mkldnn_adaptive_avg_pool2d(const Tensor & self, IntArrayRef output_size);
  Tensor & avg_pool2d_out_out(Tensor & out, const Tensor & self, IntArrayRef kernel_size, IntArrayRef stride, IntArrayRef padding, bool ceil_mode, bool count_include_pad, c10::optional<int64_t> divisor_override);
  Tensor avg_pool2d(const Tensor & self, IntArrayRef kernel_size, IntArrayRef stride, IntArrayRef padding, bool ceil_mode, bool count_include_pad, c10::optional<int64_t> divisor_override);
  Tensor & avg_pool3d_out_out(Tensor & out, const Tensor & self, IntArrayRef kernel_size, IntArrayRef stride, IntArrayRef padding, bool ceil_mode, bool count_include_pad, c10::optional<int64_t> divisor_override);
  Tensor avg_pool3d(const Tensor & self, IntArrayRef kernel_size, IntArrayRef stride, IntArrayRef padding, bool ceil_mode, bool count_include_pad, c10::optional<int64_t> divisor_override);
}

} // namespace at
