import cv2
import matplotlib.pyplot as plt
import time
# // 图像亮度均衡化
# void ImageStitcher::lightBalance(Mat& img)
# {
#         int h = img.rows, w = img.cols;
#         // 转换颜色空间
#         Mat hsv;
#         vector<Mat> chs;
#         cvtColor(img, hsv, COLOR_BGR2HSV);
#         split(hsv, chs);
#         // 划分网格
#         int dh = h * gridSize, dw = w * gridSize;
#         int ph = 0, pw = 0;
#         Mat imgMeanV = Mat(h / dh, w / dw, CV_32F);
#         for (int i = 0; i < imgMeanV.rows; i++) {
#                 float* itemV = imgMeanV.ptr<float>(i);
#                 pw = 0;
#                 for (int j = 0; j < imgMeanV.cols; j++) {
#                         *itemV = (float)(mean(chs[2](Rect(pw, ph, dw, dh))).val[0]);
#                         itemV++;
#                         pw += dw;
#                 }
#                 ph += dh;
#         }
#         // 还原原图大小
#         Mat imgRatioV;
#         resize(imgMeanV, imgRatioV, Size(w, h));
#         // 亮度均衡
#         int pixVal = 0;
#         for (int i = 0; i < h; i++) {
#                 uchar* pixV = chs[2].ptr<uchar>(i);
#                 float* itemV = imgRatioV.ptr<float>(i);
#                 for (int j = 0; j < w; j++) {
#                         pixVal = *pixV / *itemV * lightness;
#                         *pixV = pixVal > 255 ? 255 : pixVal;
#
#                         pixV++;
#                         itemV++;
#                 }
#         }
#         //Mat result;
#         merge(chs, img);
#         cvtColor(img, img, COLOR_HSV2BGR);
#
# }
import numpy as np
def lightBalance(img,gridSize=10,lightness=50):
    # 转换颜色空间
    hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
    # chs = cv2.split(hsv)
    Hue, Saturation, Value=cv2.split(hsv)
    print(Hue.shape,Saturation.shape,Value.shape)
    # 划分网格
    dh = int(img.shape[0] / gridSize)
    dw = int(img.shape[1] / gridSize)
    ph = 0
    imgMeanV = np.zeros([int(img.shape[0] / dh), int(img.shape[1] / dw)])
    for i in range(imgMeanV.shape[0]):
        itemV = imgMeanV[i]
        pw = 0
        for j in range(imgMeanV.shape[1]):
            itemV[j] = np.mean(Value[ph:ph+dh,pw:pw+dw])
            pw += dw
        ph += dh
    # 还原原图大小
    imgRatioV=cv2.resize(imgMeanV,(img.shape[1], img.shape[0]))
    # 亮度均衡
    Value = Value / imgRatioV * lightness
    result=cv2.merge((Hue,Saturation,np.array(Value,dtype=np.uint8)))
    result=cv2.cvtColor(result, cv2.COLOR_HSV2BGR)
    return result


if __name__ == '__main__':
    img1=cv2.imread('A3_01_01_01.jpg')
    starttime=time.time()
    result=lightBalance(img1)
    print(time.time()-starttime)
    #cv2.imwrite('result2.jpg',result)
    gray=cv2.cvtColor(result,cv2.COLOR_BGR2GRAY)
    ret,thred=cv2.threshold(gray, 32, 255, cv2.THRESH_BINARY)
    cv2.imwrite('result_3.jpg',thred)
    plt.imshow(thred)
    plt.show()

# img2=cv2.imread("map.png",0)
# result=cv2.bitwise_and(img1,img1,mask=img2)
# cv2.imwrite("result.png",result)